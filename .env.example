PORT=3000
# PostgreSQL Database
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=minos

# WATI Integration
WATI_BASE_URL=https://live-mt-server.wati.io/
WATI_TOKEN=tu_token_aqui
BACKEND_URL=http://localhost:3000

ENCRYPTION_KEY_BASE64=key_base64

# TinyURL Integration
TINYURL_API_BASE=https://api.tinyurl.com
TINYURL_API_TOKEN=tu_tinyurl_token_aqui
TINYURL_TIMEOUT_MS=10000
