# =================================================================
#            EXAMPLE ENVIRONMENT VARIABLES
# =================================================================
#
# Este archivo sirve como plantilla. Copia este archivo a un nuevo
# archivo llamado .env y rellena los valores correspondientes.
# ¡NUNCA subas tu archivo .env a un repositorio de código!
#

# Application Configuration
# -----------------------------------------------------------------
PORT=3000
BACKEND_URL=http://localhost:3000
NODE_ENV=development # development | production

# Frontend Configuration
# -----------------------------------------------------------------
# Dominios y URLs completas de las aplicaciones cliente que se conectarán
# a este backend. Se usan principalmente para configurar CORS.
#
# Los DOMAIN son sin http/https (ej: localhost o midominio.com).
# Los URL son completos (ej: http://localhost:4200 o https://admin.midominio.com).
#
# FRONTEND_URL_CLIENT: URL de la aplicación orientada al cliente final.
# FRONTEND_URL_ADMIN: URL del panel de administración.
#
FRONTEND_DOMAIN_CLIENT=localhost
FRONTEND_DOMAIN_ADMIN=localhost
FRONTEND_URL_CLIENT=http://localhost:4200
FRONTEND_URL_ADMIN=http://localhost:4201

# PostgreSQL Database Configuration
# -----------------------------------------------------------------
# Configuración para la conexión con la base de datos PostgreSQL.
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=
DB_DATABASE=minos_db

# JSON Web Tokens (JWT) Configuration
# -----------------------------------------------------------------
# Las claves secretas DEBEN ser cadenas de texto largas y aleatorias
# generadas de forma segura (ej: openssl rand -base64 48).
# No dejes estos valores en blanco en tu archivo .env.
JWT_SECRET_KEY_ACCESS=
JWT_SECRET_KEY_REFRESH=

# Tiempos de expiración de los tokens.
# (s = segundos, m = minutos, h = horas, d = días)
JWT_EXPIES_IN_ACCESS=3h
JWT_EXPIRES_IN_REFRESH=7d

# WATI Integration
# -----------------------------------------------------------------
# Credenciales para la integración con el servicio de WATI.
WATI_BASE_URL=https://live-mt-server.wati.io/
WATI_TOKEN=tu_token_aqui
BACKEND_URL=http://localhost:3000

ENCRYPTION_KEY_BASE64=key_base64

# TinyURL Integration
TINYURL_API_BASE=https://api.tinyurl.com
TINYURL_API_TOKEN=tu_tinyurl_token_aqui
TINYURL_TIMEOUT_MS=10000
