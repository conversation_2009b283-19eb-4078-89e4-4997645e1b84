import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';
import { ApolloServerPluginLandingPageLocalDefault } from '@apollo/server/plugin/landingPage/default';
import { ApolloServerPlugin } from '@apollo/server';

const plugins: ApolloServerPlugin[] = [];

if (process.env.NODE_ENV !== 'production') {
  plugins.push(ApolloServerPluginLandingPageLocalDefault());
}

export const graphqlConfig: ApolloDriverConfig = {
  driver: ApolloDriver,
  autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
  playground: false,
  plugins,
  buildSchemaOptions: {
    dateScalarMode: 'timestamp',
  },
  context: ({ req }) => ({ req }),

  // Development settings
  // playground: process.env.NODE_ENV !== 'production',
  introspection: process.env.NODE_ENV !== 'production',
  includeStacktraceInErrorResponses: process.env.NODE_ENV !== 'production',

  // Security settings
  csrfPrevention: process.env.NODE_ENV === 'production',

  // Performance settings
  // installSubscriptionHandlers: true,
  // fieldResolverEnhancers: ['guards', 'interceptors', 'filters'],

  // Additional settings for better development experience
  formatError: (error) => {
    // Remove stack traces in production
    if (process.env.NODE_ENV === 'production') {
      delete error.extensions?.stacktrace;
    }
    return error;
  },
};
