import { ObjectType, Field } from '@nestjs/graphql';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { AuditableEntity } from '../../../common/entities/auditable.entity';
import { CampaignRegistration } from 'src/modules/campaign-registrations/entities/campaign-registration.entity';

@ObjectType()
@Entity({ name: 'users' })
export class User extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String)
  id: string;

  // @Column({ length: 20, unique: true })
  // @Field(() => String)
  // dni: string;

  @Column({ length: 20, unique: true })
  @Field(() => String)
  phone: string;

  @Field(()=> [CampaignRegistration], { nullable: true })
  @OneToMany(() => CampaignRegistration, (campaignRegistration) => campaignRegistration.user)
  campaign_registrations?: CampaignRegistration[];
}
