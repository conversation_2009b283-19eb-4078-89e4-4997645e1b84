import { ObjectType, Field } from '@nestjs/graphql';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { AuditableEntity } from '../../../common/entities/auditable.entity';
import { CampaignRegistration } from 'src/modules/campaign-registrations/entities/campaign-registration.entity';
import { Credential } from 'src/modules/credentials/entities/credential.entity';
import { UserRole } from 'src/modules/roles-permissions-management/users_roles/entities/user_role.entity';
import {
  IsEmail,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

@ObjectType()
@Entity({ name: 'users' })
export class User extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String)
  id: string;

  @Field(() => String, { nullable: true })
  @Column({ type: 'varchar', length: 255, unique: false, nullable: true })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field(() => String, { nullable: true })
  @Column({ type: 'varchar', length: 100, unique: true, nullable: true })
  @IsString()
  @IsOptional()
  @Length(3, 100)
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      'El nombre de usuario solo puede contener letras, números, guiones bajos y puntos.',
  })
  username?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Nombre completo del usuario',
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  full_name?: string;

  // @Column({ length: 20, unique: true })
  // @Field(() => String)
  // dni: string;

  @Column({ length: 20, unique: true })
  @Field(() => String)
  phone: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @Field(() => String, { nullable: true })
  profile_picture?: string;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @Field(() => [CampaignRegistration], { nullable: true })
  @OneToMany(
    () => CampaignRegistration,
    (campaignRegistration) => campaignRegistration.user,
  )
  campaign_registrations?: CampaignRegistration[];

  @OneToOne(() => Credential, (credential) => credential.user)
  @Field(() => Credential, { nullable: true })
  credential?: Credential;

  @OneToMany(() => UserRole, (user_role) => user_role.user)
  @Field(() => [UserRole], { nullable: true })
  users_roles?: UserRole[];
}
