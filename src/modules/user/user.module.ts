import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserResolver } from './user.resolver';
import { UserController } from './user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { CampaignModule } from '../campaign/campaign.module';
import { CampaignRegistrationsModule } from '../campaign-registrations/campaign-registrations.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [ 
    TypeOrmModule.forFeature([User]),
    CampaignModule,
    ConfigModule
  ],
  providers: [UserResolver, UserService],
  controllers: [UserController],
  exports: [UserService]
})
export class UserModule {}
