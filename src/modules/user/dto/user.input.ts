import { Field, InputType, PartialType } from '@nestjs/graphql';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
} from 'class-validator';

@InputType()
export class InsertUserInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  @Length(3, 100)
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      'El nombre de usuario solo puede contener letras, números, guiones bajos y puntos.',
  })
  username?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Nombre completo del usuario',
  })
  full_name?: string;

  @Field(() => String)
  @IsString()
  phone: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  profile_picture?: string;
}

@InputType()
export class UpdateUserInput extends PartialType(InsertUserInput) {
  @IsUUID()
  @Field(() => String)
  id: string;
}
