import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';

import { ConfigService } from '@nestjs/config';
import { Campaign } from '../campaign/entities/campaign.entity';
import { WebhookNewContactMessagePayload } from 'src/integrations/wati/wati.service';
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {}



  findUser_ById(id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  findUser_ByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { phone } });
  }

  async saveUser(data: WebhookNewContactMessagePayload): Promise<User> {
    const { waId, senderName } = data;
    let user = await this.findUser_ByPhone(waId);
    if(user) {
      return user
    }else{
      const userEntity: User = this.userRepository.create({
        phone: waId,
      });
      const userSaved: User = await this.userRepository.save(userEntity);
      return userSaved;
    }
  }

  async createUser(phone:string): Promise<User> {
    const userEntity: User = this.userRepository.create({
      phone: phone,
    });
    const userSaved: User = await this.userRepository.save(userEntity);
    return userSaved;
  }
  

}
