import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';

import { ConfigService } from '@nestjs/config';
import { WebhookNewContactMessagePayload } from 'src/integrations/wati/wati.service';
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {}

  findUser_ById(id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  findUser_ByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { phone } });
  }

  async saveUser(data: WebhookNewContactMessagePayload): Promise<User> {
    const { waId, senderName } = data;
    let user = await this.findUser_ByPhone(waId);
    if (user) {
      return user;
    } else {
      const userEntity: User = this.userRepository.create({
        phone: waId,
      });
      const userSaved: User = await this.userRepository.save(userEntity);
      return userSaved;
    }
  }

  async createUser(phone: string): Promise<User> {
    const userEntity: User = this.userRepository.create({
      phone: phone,
    });
    const userSaved: User = await this.userRepository.save(userEntity);
    return userSaved;
  }

  /**
   * Nota: Posiblemente se quitar más adelante.
   *
   * Obtiene un usuario por su email.
   * @param email El email del usuario.
   * @throws NotFoundException si no se encuentra el usuario.
   * @returns La entidad User.
   */
  async getUser_ByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException(`Usuario con email ${email} no encontrado`);
    }

    return user;
  }

  /**
   * Obtiene un usuario por su ID.
   * @param id El ID del usuario.
   * @throws NotFoundException si no se encuentra el usuario.
   * @returns La entidad User.
   */
  async getUser_ById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
    }

    return user;
  }

  /**
   * Obtiene un usuario por su nombre de usuario único.
   *
   * @param username El nombre de usuario a buscar.
   * @returns La entidad User si se encuentra.
   * @throws {NotFoundException} Si no se encuentra ningún usuario con ese username.
   */
  async findOneByUsername(username: string): Promise<User> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .where('user.username = :username', { username: username })
      .getOne();

    if (!user)
      throw new NotFoundException(
        `Usuario con el nombre de usuario '${username}' no encontrado`,
      );

    return user;
  }
}
