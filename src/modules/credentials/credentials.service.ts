import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Credential } from './entities/credential.entity';
import { Connection, Repository } from 'typeorm';
import { UserService } from '../user/user.service';
import {
  Payload_Access,
  Payload_Refresh,
} from '../auth/interfaces/auth.interface';
import * as bcrypt from 'bcrypt';
import {
  CredentialsResponse,
  RoleAndPermissionsResponse,
} from './dto/credential.dto';
import { UserRole } from '../roles-permissions-management/users_roles/entities/user_role.entity';
import { User } from '../user/entities/user.entity';
import { v4 as uuidv4 } from 'uuid';
import { Role } from '../roles-permissions-management/roles/entities/role.entity';
import { JwtService } from '@nestjs/jwt';
import { createPayloads } from '../auth/utils/payloads.util';
import { UsersRolesService } from '../roles-permissions-management/users_roles/users_roles.service';
import { CreateAccountDto } from './dto/credential.input';
import { InsertUserInput } from '../user/dto/user.input';
import { RolesService } from '../roles-permissions-management/roles/roles.service';
import { ERole } from 'src/common/enums/role.enum';

@Injectable()
export class CredentialsService {
  constructor(
    @InjectRepository(Credential)
    private readonly credentialRepository: Repository<Credential>,
    private readonly userService: UserService,
    private readonly jwt_service: JwtService,
    private readonly usersRolesService: UsersRolesService,
    private readonly rolesService: RolesService,
    private readonly connection: Connection,
  ) {}

  /**
   * Autentica a un usuario cliente con su username y contraseña.
   * Valida que el usuario exista, esté verificado y la contraseña sea correcta.
   *
   * @param username Nombre de usuario del usuario.
   * @param password Contraseña en texto plano.
   * @returns Los payloads para los tokens y los permisos del usuario.
   * @throws {UnauthorizedException} Si las credenciales son inválidas.
   */
  async loginWithUsernameAndPassword(
    username: string,
    password: string,
  ): Promise<CredentialsResponse> {
    try {
      const { user, credential, codes_roles } =
        await this.validateUserByUsername(username);

      const allowedClientRoles = [ERole.COORDINADOR, ERole.LIDER];

      const hasRequiredRole = codes_roles.some((userRole) =>
        allowedClientRoles.includes(userRole as ERole),
      );

      if (!hasRequiredRole) {
        throw new UnauthorizedException(
          `El usuario no tiene un rol válido para acceder a este portal.`,
        );
      }

      if (!credential.password) {
        throw new UnauthorizedException(
          'El usuario no tiene una contraseña configurada para este método de login.',
        );
      }

      const isMatch = await bcrypt.compare(password, credential.password);
      if (!isMatch) throw new UnauthorizedException('Contraseña incorrecta.');

      const { role_active, permissions } =
        await this.getRoleActivaAndPermissions(codes_roles, user);

      const { payload_access, payload_refresh } = await createPayloads(
        user,
        codes_roles,
        role_active,
        false,
      );

      return { payload_access, payload_refresh, permissions };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Autentica a un usuario administrador con su username y contraseña.
   * Valida que el usuario tenga roles de administrador y que la contraseña sea correcta.
   *
   * @param username Nombre de usuario del administrador.
   * @param password Contraseña en texto plano.
   * @returns Los payloads para los tokens y los permisos del rol de administrador activo.
   * @throws {UnauthorizedException} Si el usuario no es un administrador o las credenciales son incorrectas.
   */
  async loginWithUsernameAndPasswordAdmin(
    username: string,
    password: string,
  ): Promise<CredentialsResponse> {
    try {
      const { user, credential, codes_roles } =
        await this.validateUserByUsername(username);

      const allowedAdminRoles = [ERole.SUPER_ADMIN, ERole.ADMIN];

      const hasAdminRole = codes_roles.some((userRole) =>
        allowedAdminRoles.includes(userRole as ERole),
      );

      if (!hasAdminRole) {
        throw new UnauthorizedException(
          `El usuario no tiene los permisos necesarios para acceder al panel de administración.`,
        );
      }

      if (!credential.password) {
        throw new UnauthorizedException(
          'El usuario no tiene una contraseña configurada.',
        );
      }

      const isMatch = await bcrypt.compare(password, credential.password);
      if (!isMatch) {
        throw new UnauthorizedException(`Contraseña incorrecta.`);
      }

      const priorityOrder = [ERole.SUPER_ADMIN, ERole.ADMIN];

      const role_activo = priorityOrder.find((priorityRole) =>
        codes_roles.includes(priorityRole),
      );

      if (!role_activo) {
        throw new UnauthorizedException(
          'El usuario no tiene un rol de administrador válido para activar.',
        );
      }

      const permissions =
        await this.usersRolesService.getPermissionsByUserAndRole(
          user.id,
          role_activo,
        );

      const { payload_access, payload_refresh } = await createPayloads(
        user,
        codes_roles,
        role_activo,
        false,
      );

      return { payload_access, payload_refresh, permissions };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Refresca el access token utilizando un refresh token válido.
   * @param refreshTokenOld El refresh token recibido desde la cookie.
   * @returns Nuevos payloads para los tokens y la lista de permisos actualizada.
   * @throws {UnauthorizedException} Si el refresh token es inválido o ha expirado.
   */
  async refreshAccessToken(
    refreshTokenOld: string,
  ): Promise<CredentialsResponse> {
    const payload: Payload_Refresh = await this.jwt_service.verifyAsync(
      refreshTokenOld,
      { secret: process.env.JWT_SECRET_KEY_REFRESH },
    );

    if (!payload) throw new UnauthorizedException('Token inválido');

    const user = await this.userService.getUser_ById(payload.sub);
    const rolesPersona = await this.rolesService.getRolesByUserId(payload.sub);
    const codes_roles: string[] = rolesPersona.map((rol: Role) => rol.name);

    const permissions =
      await this.usersRolesService.getPermissionsByUserAndRole(
        payload.sub,
        payload.role_active,
      );

    const { payload_access, payload_refresh } = await createPayloads(
      user,
      codes_roles,
      payload.role_active,
      false,
    );
    return { payload_access, payload_refresh, permissions };
  }

  /**
   * Cambia el rol activo de un usuario y regenera los tokens con los nuevos permisos.
   * @param token El refresh token que identifica al usuario y su sesión.
   * @param rol El nuevo código de rol que se desea activar.
   * @returns Nuevos payloads para los tokens y la lista de permisos del nuevo rol.
   * @throws {Error} Si se intenta cambiar a un rol que el usuario no posee.
   */
  async changeRole(token: string, rol: string): Promise<CredentialsResponse> {
    const payload = await this.jwt_service.verifyAsync(token, {
      secret: process.env.JWT_SECRET_KEY_REFRESH,
    });

    if (payload.role_active === rol) {
      throw new Error('No se puede cambiar al mismo rol que ya está activo.');
    }

    const user = await this.userService.getUser_ById(payload.sub);
    const roles_user = await this.rolesService.getRolesByUserId(payload.sub);

    if (!roles_user.some((r) => r.name === rol)) {
      throw new Error('El usuario no posee el rol al que intenta cambiar.');
    }

    const codes_roles: string[] = roles_user.map((rol: Role) => rol.name);
    const permissions =
      await this.usersRolesService.getPermissionsByUserAndRole(
        payload.sub,
        rol,
      );

    const { payload_access, payload_refresh } = await createPayloads(
      user,
      codes_roles,
      rol,
      false,
    );

    return { payload_access, payload_refresh, permissions };
  }

  // /**
  //  * Gestiona el proceso completo de registro de un nuevo usuario en una transacción.
  //  * Incluye la creación del usuario, sus credenciales y la asignación del rol por defecto.
  //  *
  //  * @param create_account_dto DTO con la información del usuario y su contraseña.
  //  * @returns {Promise<boolean>} True si la cuenta fue creada exitosamente.
  //  * @throws {Error} Si el email ya está en uso o si faltan datos.
  //  */
  // async createAccountUser(
  //   create_account_dto: CreateAccountDto,
  // ): Promise<boolean> {
  //   const query_runner = this.connection.createQueryRunner();
  //   await query_runner.connect();
  //   await query_runner.startTransaction();

  //   try {
  //     const existingUser = await this.userService.getUser_ByEmail(
  //       create_account_dto.create_user_dto.email,
  //     );

  //     if (existingUser) {
  //       throw new Error(`El correo electrónico ya está en uso.`);
  //     }

  //     const user_new = this.createUserEntity(
  //       create_account_dto.create_user_dto,
  //     );
  //     const credential_new = this.createCredentialEntity(
  //       create_account_dto.password!,
  //       user_new,
  //     );
  //     const user_role_new = await this.createUserRoleEntity(user_new);

  //     await query_runner.manager.save(user_new);
  //     await query_runner.manager.save(credential_new);
  //     await query_runner.manager.save(user_role_new);

  //     await query_runner.commitTransaction();

  //     // this.mail_service.sendUserVerificationCode(
  //     //   user_new.email,
  //     //   `${user_new.first_name} ${user_new.last_name}`,
  //     //   user_new.verification_code,
  //     // );

  //     return true;
  //   } catch (e) {
  //     await query_runner.rollbackTransaction();
  //     throw e;
  //   } finally {
  //     await query_runner.release();
  //   }
  // }

  /**
   * Valida la existencia y estado de un usuario y sus credenciales.
   * Encapsula la lógica común para los procesos de login.
   * @param email El email del usuario a validar.
   * @returns Un objeto con la entidad del usuario, sus credenciales y sus roles.
   */
  private async validateUser(
    email: string,
  ): Promise<{ user: User; credential: Credential; codes_roles: string[] }> {
    const user = await this.userService.findOneByUsername(email);

    const credential = await this.getCredentialByUserId(user.id);
    if (!credential || !credential.password) {
      throw new UnauthorizedException(
        `El usuario no tiene credenciales de contraseña.`,
      );
    }

    const roles_user = await this.rolesService.getRolesByUserId(user.id);
    const codes_roles: string[] = roles_user.map((rol: Role) => rol.name);
    return { user, credential, codes_roles };
  }

  /**
   * Valida la existencia y estado de un usuario y sus credenciales por su username.
   * Encapsula la lógica común para el proceso de login por nombre de usuario.
   *
   * @param username El username del usuario a validar.
   * @returns Un objeto con la entidad del usuario, sus credenciales y sus roles.
   * @throws {UnauthorizedException} Si el usuario, sus credenciales o roles no son válidos.
   */
  private async validateUserByUsername(
    username: string,
  ): Promise<{ user: User; credential: Credential; codes_roles: string[] }> {
    const user = await this.userService.findOneByUsername(username);

    const credential = await this.getCredentialByUserId(user.id);
    if (!credential)
      throw new UnauthorizedException(
        `El usuario no tiene credenciales de contraseña.`,
      );

    const roles_user = await this.rolesService.getRolesByUserId(user.id);
    const codes_roles: string[] = roles_user.map((rol: Role) => rol.name);

    return { user, credential, codes_roles };
  }

  /**
   * Obtiene las credenciales de un usuario por su ID.
   * @param user_id El ID del usuario.
   * @returns La entidad Credential o undefined si no se encuentra.
   */
  private async getCredentialByUserId(user_id: string) {
    return await this.credentialRepository
      .createQueryBuilder('credential')
      .innerJoin('credential.user', 'user')
      .where('user.id = :user_id', { user_id })
      .getOne();
  }

  /**
   * Determina el rol activo y obtiene los permisos asociados para un usuario cliente.
   * @param codes_roles Array con los códigos de los roles del usuario.
   * @param user La entidad del usuario.
   * @returns El rol activo y la lista de permisos.
   */
  private async getRoleActivaAndPermissions(
    codes_roles: string[],
    user: User,
  ): Promise<RoleAndPermissionsResponse> {
    const role_active = this.getRoleActive(codes_roles);
    const permissions =
      await this.usersRolesService.getPermissionsByUserAndRole(
        user.id,
        role_active,
      );
    return { role_active, permissions };
  }

  /**
   * Determina cuál es el rol activo por defecto para un usuario.
   * @param codes_roles Array con los códigos de los roles del usuario.
   * @returns El código del rol activo.
   */
  private getRoleActive(codes_roles: string[]): string {
    if (codes_roles.includes(ERole.USUARIO)) {
      return ERole.USUARIO;
    }
    if (codes_roles.length > 0) {
      return codes_roles[0];
    }
    throw new UnauthorizedException(`El usuario no tiene roles asignados.`);
  }

  /**
   * Crea una nueva instancia de la entidad User lista para ser guardada.
   * @param create_user_dto Los datos para crear el usuario.
   * @returns Una entidad User.
   */
  private createUserEntity(create_user_dto: InsertUserInput): User {
    const user_new = new User();
    Object.assign(user_new, create_user_dto);
    user_new.id = uuidv4();
    return user_new;
  }

  /**
   * Crea una nueva instancia de la entidad Credential lista para ser guardada.
   * @param password La contraseña en texto plano (se hashea automáticamente por la entidad).
   * @param user La entidad User a la que se asocian las credenciales.
   * @returns Una entidad Credential.
   */
  private createCredentialEntity(password: string, user: User): Credential {
    const credential_new = new Credential();
    credential_new.id = uuidv4();
    credential_new.password = password;
    credential_new.user = user;
    return credential_new;
  }

  /**
   * Crea una nueva instancia de la entidad UserRole para asignar el rol por defecto.
   * @param user La entidad User a la que se le asignará el rol.
   * @returns Una entidad UserRole.
   */
  private async createUserRoleEntity(user: User): Promise<UserRole> {
    const user_role_new = new UserRole();
    user_role_new.id = uuidv4();
    user_role_new.user = user;
    user_role_new.role = await this.rolesService.getRoleUser();
    return user_role_new;
  }
}
