import { Field, InputType } from '@nestjs/graphql';
import {
  IsBoolean,
  IsEmail,
  IsOptional,
  IsString,
  Length,
  Matches,
  MinLength,
} from 'class-validator';

@InputType()
export class CreateAccountUserDto {
  @IsString()
  @Length(3, 255)
  @Field(() => String)
  full_name: string;

  @IsEmail()
  @Length(3, 50)
  @Field(() => String)
  email: string;

  @Field(() => Boolean, { nullable: false })
  @IsBoolean()
  terms_accepted: boolean;
}

@InputType()
export class CreateAccountDto {
  @Field({ nullable: true })
  @IsOptional()
  @MinLength(6, { message: 'Password must have at least 6 characters' })
  @Matches(/^(?=.*\d)(?=.*[A-Z])(?=.*[\W_]).*$/, {
    message:
      '/La contraseña debe tener al menos 6 caracteres, 1 número, 1 letra mayúscula y 1 carácter especial.',
  })
  password?: string;

  @Field()
  create_user_dto: CreateAccountUserDto;
}
