import { ObjectType, Field } from '@nestjs/graphql';
import {
  Payload_Access,
  Payload_Refresh,
} from 'src/modules/auth/interfaces/auth.interface';

@ObjectType()
export class CredentialsResponse {
  @Field()
  payload_access: Payload_Access;

  @Field()
  payload_refresh: Payload_Refresh;

  @Field(() => [String])
  permissions: string[];
}

@ObjectType()
export class RoleAndPermissionsResponse {
  @Field(() => String)
  role_active: string;

  @Field(() => [String])
  permissions: string[];
}
