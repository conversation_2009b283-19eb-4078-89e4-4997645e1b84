import { ObjectType, Field } from '@nestjs/graphql';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import {
  BeforeInsert,
  Column,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { User } from 'src/modules/user/entities/user.entity';

@ObjectType()
@Entity({ name: 'credentials' })
export class Credential extends AuditableEntity {
  @IsUUID()
  @Field(() => String)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ default: null })
  @IsNotEmpty({ message: 'Password is not empty' })
  password?: string;

  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @OneToOne(() => User, (user) => user.credential)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
