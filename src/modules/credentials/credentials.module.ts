import { forwardRef, Module } from '@nestjs/common';
import { CredentialsService } from './credentials.service';
import { CredentialsResolver } from './credentials.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Credential } from './entities/credential.entity';
import { UserModule } from '../user/user.module';
import { UsersRolesModule } from '../roles-permissions-management/users_roles/users_roles.module';
import { RolesModule } from '../roles-permissions-management/roles/roles.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Credential]),
    UserModule,
    UsersRolesModule,
    RolesModule,

    forwardRef(() => AuthModule),
  ],
  providers: [CredentialsResolver, CredentialsService],
  exports: [CredentialsService],
})
export class CredentialsModule {}
