import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { CredentialsService } from './credentials.service';
import { Credential } from './entities/credential.entity';
import { Public } from 'src/common/decorators/public.decorator';
import * as bcrypt from 'bcrypt';

@Resolver(() => Credential)
export class CredentialsResolver {
  constructor(private readonly credentialsService: CredentialsService) {}

  /**
   * **[UTILIDAD DE DESARROLLO]** Convierte una contraseña en un hash bcrypt.
   * Este endpoint debe ser deshabilitado en entornos de producción.
   *
   * @param password La contraseña en texto plano que se desea hashear.
   * @returns Una promesa que resuelve al string del hash bcrypt.
   */
  @Public()
  @Query(() => String)
  async generateHash(@Args('password') password: string): Promise<string> {
    return await bcrypt.hash(password, 10);
  }
}
