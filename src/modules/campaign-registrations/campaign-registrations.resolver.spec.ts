import { Test, TestingModule } from '@nestjs/testing';
import { CampaignRegistrationsResolver } from './campaign-registrations.resolver';

describe('CampaignRegistrationsResolver', () => {
  let resolver: CampaignRegistrationsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CampaignRegistrationsResolver],
    }).compile();

    resolver = module.get<CampaignRegistrationsResolver>(CampaignRegistrationsResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
