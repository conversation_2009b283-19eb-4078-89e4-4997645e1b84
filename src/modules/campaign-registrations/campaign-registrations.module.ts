import { Module } from '@nestjs/common';
import { CampaignRegistrationsResolver } from './campaign-registrations.resolver';
import { CampaignRegistrationsService } from './campaign-registrations.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CampaignRegistration } from './entities/campaign-registration.entity';
import { UserModule } from '../user/user.module';
import { CampaignModule } from '../campaign/campaign.module';

@Module({
  imports:[ 
    TypeOrmModule.forFeature( [CampaignRegistration] ),
    UserModule,
    CampaignModule
  ],
  providers: [CampaignRegistrationsResolver, CampaignRegistrationsService],
  exports: [CampaignRegistrationsService],
})
export class CampaignRegistrationsModule {}
