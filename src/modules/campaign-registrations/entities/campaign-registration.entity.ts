import { Field, ObjectType } from '@nestjs/graphql';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { Campaign } from 'src/modules/campaign/entities/campaign.entity';
import { User } from 'src/modules/user/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@ObjectType()
@Entity({ name: 'campaign_registrations' })
export class CampaignRegistration extends AuditableEntity {
  @Field(() => String)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Field(() => String, { nullable: true })
  campaign_id: string;
  
  @Field(()=> Campaign, {nullable:true})
  @ManyToOne(() => Campaign, (campaign) => campaign.campaign_registrations)
  @JoinColumn({ name: 'campaign_id' })
  campaign: Campaign;

  @Column({ type: 'uuid', nullable: false })
  @Field(() => String, { nullable: true })
  user_id: string;
  
  @Field(()=> User, {nullable:true})
  @ManyToOne(() => User, (user) => user.campaign_registrations)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ length: 100 })
  @Field(() => String)
  name: string;

  @Column({ length: 50, unique: true })
  @Field(() => String)
  invite_code: string;

  @Column({ length: 100, unique: true })
  @Field(() => String, { nullable: true })
  invite_link: string;

  @Column({ type: 'uuid', nullable: true })
  @Field(() => String, { nullable: true })
  referred_by_user_id: string | null;

  @Column({ type: 'jsonb', nullable: true })
  @Field(() => String, { nullable: true })
  registration_form: any | null;

  @Column({ type: 'string', nullable:true})
  @Field(()=> String, {nullable: true})
  token_access_red
}
