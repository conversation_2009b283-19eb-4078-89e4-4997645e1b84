import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CampaignRegistration } from './entities/campaign-registration.entity';
import { Repository } from 'typeorm';
import { CreateCampaignRegistrationInput } from './dto/create-campaign-registration.input';
import { User } from '../user/entities/user.entity';
import { v4 as uuidv4 } from 'uuid';
import { WebhookNewContactMessagePayload } from 'src/integrations/wati/wati.service';
import { ConfigService } from '@nestjs/config';
import { generateInviteUrl, generateMyNetworkUrl, ROUTES } from 'src/common/constants/routes.constants';
import { UserService } from '../user/user.service';
import { CampaignService } from '../campaign/campaign.service';
import { WatiAtributeContactData } from 'src/integrations/wati/wati.controller';

@Injectable()
export class CampaignRegistrationsService {
  constructor(
    @InjectRepository(CampaignRegistration)
    private campaignRegistrationRepository: Repository<CampaignRegistration>,
    private readonly configService: ConfigService,
    private readonly usersService: UserService,
    private readonly campaignsService: CampaignService,
  ) {}

  async getCampaignRegistrationByCampaignIdAndUserId(
    campaignId: string,
    userId: string,
  ): Promise<CampaignRegistration | null> {
    return await this.campaignRegistrationRepository.findOne({
      where: { campaign_id: campaignId, user_id: userId },
    });
  }

  async getCampaignRegistrationByInviteCode(
    inviteCode: string,
  ): Promise<CampaignRegistration | null> {
    return await this.campaignRegistrationRepository.findOne({
      where: { invite_code: inviteCode },
    });
  }

  async insertCampaignRegistration(
    campaignId: string,
    user: User,
    payload: WebhookNewContactMessagePayload,
  ): Promise<CampaignRegistration> {
    const inviteCode = uuidv4();
    const campaignRegistrationInput: CreateCampaignRegistrationInput = {
      campaign_id: campaignId,
      user_id: user.id,
      name: payload.senderName,
      invite_code: inviteCode,
      invite_link: this.generateInviteLink(inviteCode),
      referred_by_user_id: null,
      registration_form: null,
    };
    const campaignRegistration = this.campaignRegistrationRepository.create(
      campaignRegistrationInput,
    );
    console.log('campaignRegistration', campaignRegistration);
    const campaignRegistrationSaved = await this.campaignRegistrationRepository
      .save(campaignRegistration)
      .catch((error) => {
        console.log(error);
      });
    if (!campaignRegistrationSaved) {
      throw new Error('Failed to save campaign registration');
    }
    return campaignRegistrationSaved;
  }

  generateInviteLink(inviteCode: string): string {
    const backendUrl = this.configService.get<string>('BACKEND_URL');
    if (!backendUrl) {
      throw new Error('BACKEND_URL not configured in environment');
    }
    return generateInviteUrl(backendUrl, inviteCode);
  }

  async updateCampaignRegistration(campaignRegistration: CampaignRegistration) {
    return await this.campaignRegistrationRepository.save(campaignRegistration);
  }

  async getCampaignRegistrationByCampaignId(
    campaignId: string,
    userId: string,
  ): Promise<CampaignRegistration | null> {
    return await this.campaignRegistrationRepository.findOne({
      where: { campaign_id: campaignId, user_id: userId },
    });
  }

  async updateRegistrationForm(
    tenantId: string,
    phone: string,
    registrationForm: any,
  ): Promise<void> {
    const user = await this.usersService.findUser_ByPhone(phone);
    if (!user) {
      throw new NotFoundException(`User with phone ${phone} not found`);
    }
    const campaign =
      await this.campaignsService.getCampaignByTenantId(tenantId);
    if (!campaign) {
      throw new NotFoundException(
        `Campaign with tenantId ${tenantId} not found`,
      );
    }
    let campaignRegistration =
      await this.getCampaignRegistrationByCampaignIdAndUserId(
        campaign.id,
        user.id,
      );
    if (!campaignRegistration) {
      const inviteCode = uuidv4();
      const campaignRegistrationInput: CreateCampaignRegistrationInput = {
        campaign_id: campaign.id,
        user_id: user.id,
        name: registrationForm.name,
        invite_code: inviteCode,
        invite_link: this.generateInviteLink(inviteCode),
        referred_by_user_id: null,
        registration_form: null,
      };
      const campaignRegistrationEntity =
        this.campaignRegistrationRepository.create(campaignRegistrationInput);
      campaignRegistration = await this.campaignRegistrationRepository.save(
        campaignRegistrationEntity,
      );
    }

    campaignRegistration.registration_form = registrationForm;
    await this.updateCampaignRegistration(campaignRegistration);
  }

  async generateMyNetworkAccessLink(
    watiAtributeContactData: WatiAtributeContactData,
  ):Promise<string | void> {
    const campaign= await this.campaignsService.getCampaignByTenantId(watiAtributeContactData.tenantId);
    const user = await this.usersService.findUser_ByPhone(watiAtributeContactData.phone);
    if (!user || !campaign) {
      console.log(`User with phone ${watiAtributeContactData.phone} or campaign with tenantId ${watiAtributeContactData.tenantId} not found`);
      return;
    }
    const campaignRestration= await this.getCampaignRegistrationByCampaignIdAndUserId(campaign.id, user.id);
    if (!campaignRestration) {
      console.log(`Campaign registration for user ${user.id} in campaign ${campaign.id} not found`);
      return;
    }
    campaignRestration.session_access_code= uuidv4();
    campaignRestration.session_access_code_expires_at= new Date(Date.now() + 5 * 60 * 1000);
    const updated= await this.updateCampaignRegistration(campaignRestration);
    console.log(`Updated campaign registration with access code: ${updated.session_access_code} expiring at ${updated.session_access_code_expires_at}`);
    const baseUrl = this.configService.get<string>('BACKEND_URL');
    if (!baseUrl) {
      console.log('BACKEND_URL not configured in environment');
      return;
    }
    if (!updated.session_access_code) {
      console.log('Session access code not set');
      return;
    }
    const myNetworkUrl= generateMyNetworkUrl(baseUrl,updated.session_access_code);
    console.log(`Generated My Network URL: ${myNetworkUrl}`);
    return myNetworkUrl;
  }


}
