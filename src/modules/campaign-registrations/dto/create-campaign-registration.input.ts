import { Field, InputType } from '@nestjs/graphql';

@InputType()
export class CreateCampaignRegistrationInput {

  @Field(() => String, { nullable: true })
  campaign_id: string;

  @Field(() => String, { nullable: true })
  user_id: string;

  @Field(() => String)
  name: string;

  @Field(() => String)
  invite_code: string;

  @Field(() => String, { nullable: true })
  invite_link: string;

  @Field(() => String, { nullable: true })
  referred_by_user_id: string | null ;

  @Field(() => String, { nullable: true })
  registration_form: any;
}
