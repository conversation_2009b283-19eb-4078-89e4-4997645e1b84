import { ObjectType, Field } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import { AuditableEntity } from 'src/common/entities/auditable.entity'; // Asegúrate que la ruta es correcta
import { Permission } from 'src/modules/roles-permissions-management/permissions/entities/permission.entity';
import { Role } from 'src/modules/roles-permissions-management/roles/entities/role.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@ObjectType()
@Entity({ name: 'roles_permissions' })
export class RolePermission extends AuditableEntity {
  @Field(() => String)
  @IsUUID()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  @IsUUID()
  role_id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  @IsUUID()
  permission_id: string;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @Field(() => Role)
  @ManyToOne(() => Role, (role) => role.role_permissions)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Field(() => Permission)
  @ManyToOne(() => Permission, (permission) => permission.role_permissions)
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;
}
