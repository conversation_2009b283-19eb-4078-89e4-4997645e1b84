import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';

@InputType()
export class InsertRolePermissionInput {
  @Field(() => String)
  @IsUUID()
  role_id: string;

  @Field(() => String)
  @IsUUID()
  permission_id: string;
}

@InputType()
export class UpdateRolePermissionInput extends PartialType(
  InsertRolePermissionInput,
) {
  @IsUUID()
  @Field(() => String)
  id: string;
}
