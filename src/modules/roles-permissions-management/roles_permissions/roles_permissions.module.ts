import { Module } from '@nestjs/common';
import { RolesPermissionsService } from './roles_permissions.service';
import { RolesPermissionsResolver } from './roles_permissions.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RolePermission } from './entities/role_permission.entity';

@Module({
  imports: [TypeOrmModule.forFeature([RolePermission])],
  providers: [RolesPermissionsResolver, RolesPermissionsService],
  exports: [RolesPermissionsService],
})
export class RolesPermissionsModule {}
