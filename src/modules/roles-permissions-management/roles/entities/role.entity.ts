import { Field, ObjectType } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsUUID, Length } from 'class-validator';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { RolePermission } from '../../roles_permissions/entities/role_permission.entity';
import { UserRole } from '../../users_roles/entities/user_role.entity';

export enum ERoleScope {
  CAMPAIGN = 'campaign',
  SYSTEM = 'system',
}

@ObjectType()
@Entity({ name: 'roles' })
export class Role extends AuditableEntity {
  @IsUUID()
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String)
  id: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
    unique: true,
  })
  @Length(3, 100)
  @Field(() => String)
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  @Length(3, 255)
  @IsOptional()
  @Field(() => String, { nullable: true })
  description?: string;

  @Column({
    type: 'enum',
    enum: ERoleScope,
    default: ERoleScope.CAMPAIGN,
  })
  @IsEnum(ERoleScope)
  scope: ERoleScope;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @Field(() => [UserRole])
  @OneToMany(() => UserRole, (user_role) => user_role.role)
  users_roles: UserRole[];

  @Field(() => [RolePermission])
  @OneToMany(() => RolePermission, (role_permissions) => role_permissions.role)
  role_permissions: RolePermission[];
}
