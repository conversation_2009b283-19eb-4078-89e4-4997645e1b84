import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsOptional, IsUUID, Length } from 'class-validator';

@InputType()
export class InsertRoleInput {
  @Length(3, 100)
  @Field(() => String)
  name: string;

  @Length(3, 255)
  @IsOptional()
  @Field(() => String, { nullable: true })
  description?: string;
}

@InputType()
export class UpdateRoleInput extends PartialType(InsertRoleInput) {
  @IsUUID()
  @Field(() => String)
  id: string;
}
