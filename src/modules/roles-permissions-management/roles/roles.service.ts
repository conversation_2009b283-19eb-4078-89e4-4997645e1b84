import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InsertRoleInput, UpdateRoleInput } from './dto/role.input';
import { InjectRepository } from '@nestjs/typeorm';
import { Role } from './entities/role.entity';
import { Repository } from 'typeorm';
import { ERole } from 'src/common/enums/role.enum';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  private readonly defaultUserRoleName = ERole.USUARIO;

  /**
   * Obtiene el rol de usuario por defecto definido en las variables de entorno.
   * Este método es crucial para asignar el rol inicial a los nuevos usuarios.
   *
   * @returns La entidad `Role` del usuario por defecto.
   * @throws {NotFoundException} Si el rol no existe en la base de datos.
   * @throws {InternalServerErrorException} Si ocurre un error inesperado en la consulta.
   */
  async getRoleUser(): Promise<Role> {
    try {
      const role = await this.roleRepository
        .createQueryBuilder('role')
        .where('role.name = :name', { name: this.defaultUserRoleName })
        .cache(true)
        .getOne();

      if (!role) {
        throw new NotFoundException(
          `El rol por defecto '${this.defaultUserRoleName}' no fue encontrado en la base de datos. Asegúrate de que exista.`,
        );
      }

      return role;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Ocurrió un error al obtener el rol por defecto: ${error.message}`,
      );
    }
  }

  /**
   * Obtiene todos los roles asignados a un usuario por su ID.
   *
   * @param id El ID del usuario cuyos roles se desean obtener.
   * @returns Un array con las entidades `Role` del usuario.
   * @throws {NotFoundException} Si el usuario no tiene roles asignados.
   */
  async getRolesByUserId(user_id: string): Promise<Role[]> {
    const roles = await this.roleRepository
      .createQueryBuilder('role')
      .innerJoin('role.users_roles', 'userRole')
      .where('userRole.user_id = :id', { id: user_id })
      .getMany();

    if (!roles || roles.length === 0) {
      throw new NotFoundException(
        `No se encontraron roles asignados para el usuario con ID: ${user_id}`,
      );
    }

    return roles;
  }
}
