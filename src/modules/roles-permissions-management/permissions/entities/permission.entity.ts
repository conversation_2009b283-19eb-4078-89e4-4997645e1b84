import { ObjectType, Field, Int } from '@nestjs/graphql';
import { IsOptional, IsString, IsUUID, Length } from 'class-validator';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { EModule } from 'src/common/enums/module.enum';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { RolePermission } from '../../roles_permissions/entities/role_permission.entity';
import { PermissionOverride } from '../../permission_overrides/entities/permission_override.entity';
import { PermissionDependency } from '../../permission_dependencies/entities/permission_dependency.entity';

@ObjectType()
@Entity({ name: 'permissions' })
export class Permission extends AuditableEntity {
  @Field(() => String)
  @IsUUID()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field(() => String)
  @Column({ type: 'varchar', length: 100, unique: true })
  @IsString()
  @Length(5, 255)
  name: string;

  @Field(() => String)
  @Column({
    type: 'enum',
    enum: EModule,
    default: EModule.GENERAL,
  })
  module: EModule;

  @Field(() => String)
  @Column({ type: 'varchar', length: 100, unique: true })
  @IsString()
  code: string;

  @Field(() => String, { nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @Field(() => [RolePermission])
  @OneToMany(
    () => RolePermission,
    (role_permission) => role_permission.permission,
  )
  role_permissions: RolePermission[];

  @Field(() => [PermissionOverride])
  @OneToMany(() => PermissionOverride, (override) => override.permission)
  permission_overrides: PermissionOverride[];

  @Field(() => [PermissionDependency])
  @OneToMany(() => PermissionDependency, (dependency) => dependency.permission)
  dependencies: PermissionDependency[];

  @Field(() => [PermissionDependency])
  @OneToMany(
    () => PermissionDependency,
    (dependency) => dependency.required_permission,
  )
  required_for: PermissionDependency[];
}
