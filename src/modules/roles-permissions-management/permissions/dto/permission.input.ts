import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsOptional, IsString, IsUUID, Length } from 'class-validator';
import { EModule } from 'src/common/enums/module.enum';

@InputType()
export class InsertPermissionInput {
  @Field(() => String)
  module: EModule;

  @Field(() => String)
  @IsString()
  @Length(5, 255)
  name: string;

  @Field(() => String)
  @IsString()
  code: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  description?: string;
}

@InputType()
export class UpdatePermissionInput extends PartialType(InsertPermissionInput) {
  @IsUUID()
  @Field(() => String)
  id: string;
}
