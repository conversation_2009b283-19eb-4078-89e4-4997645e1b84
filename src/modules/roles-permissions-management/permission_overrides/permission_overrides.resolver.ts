import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { PermissionOverridesService } from './permission_overrides.service';
import { PermissionOverride } from './entities/permission_override.entity';

@Resolver(() => PermissionOverride)
export class PermissionOverridesResolver {
  constructor(
    private readonly permissionOverridesService: PermissionOverridesService,
  ) {}
}
