import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsUUID, Length } from 'class-validator';
import { EActionPermission } from 'src/common/enums/action-permission.enum';

@InputType()
export class InsertPermissionOverrideInput {
  @Field(() => String)
  @IsUUID()
  user_role_id: string;

  @Field(() => String)
  @IsUUID()
  permission_id: string;

  @Field(() => String)
  @IsEnum(EActionPermission)
  action: EActionPermission;
}

@InputType()
export class UpdatePermissionOverrideInput extends PartialType(
  InsertPermissionOverrideInput,
) {
  @IsUUID()
  @Field(() => String)
  id: string;
}
