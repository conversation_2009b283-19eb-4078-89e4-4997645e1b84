import { Module } from '@nestjs/common';
import { PermissionOverridesService } from './permission_overrides.service';
import { PermissionOverridesResolver } from './permission_overrides.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionOverride } from './entities/permission_override.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PermissionOverride])],
  providers: [PermissionOverridesResolver, PermissionOverridesService],
  exports: [PermissionOverridesService],
})
export class PermissionOverridesModule {}
