import { ObjectType, Field } from '@nestjs/graphql';
import { IsEnum, IsUUID } from 'class-validator';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { EActionPermission } from 'src/common/enums/action-permission.enum';
import { Permission } from 'src/modules/roles-permissions-management/permissions/entities/permission.entity';
import { UserRole } from 'src/modules/roles-permissions-management/users_roles/entities/user_role.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@ObjectType()
@Entity({ name: 'permission_overrides' })
export class PermissionOverride extends AuditableEntity {
  @Field(() => String)
  @IsUUID()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  @IsUUID()
  user_role_id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  @IsUUID()
  permission_id: string;

  @Field(() => String)
  @Column({
    type: 'enum',
    enum: EActionPermission,
    default: EActionPermission.GRANT,
  })
  @IsEnum(EActionPermission)
  action: EActionPermission;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @Field(() => Permission)
  @ManyToOne(() => Permission, (permission) => permission.permission_overrides)
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;

  @Field(() => UserRole)
  @ManyToOne(() => UserRole, (user_role) => user_role.permission_overrides)
  @JoinColumn({ name: 'user_role_id' })
  user_role: UserRole;
}
