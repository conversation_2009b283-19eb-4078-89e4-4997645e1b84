import { Field, ObjectType } from '@nestjs/graphql';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { User } from 'src/modules/user/entities/user.entity';
import { Role } from 'src/modules/roles-permissions-management/roles/entities/role.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PermissionOverride } from '../../permission_overrides/entities/permission_override.entity';

@ObjectType()
@Entity({ name: 'users_roles' })
export class UserRole extends AuditableEntity {
  @Field(() => String)
  @IsUUID()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  @IsString()
  user_id: string;

  @Field(() => String)
  @Column({ type: 'uuid' })
  @IsString()
  role_id: string;

  @Field(() => Boolean, { nullable: true })
  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  @IsOptional()
  personalized?: boolean;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================
  @Field(() => User)
  @ManyToOne(() => User, (user) => user.users_roles, {})
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Field(() => Role)
  @ManyToOne(() => Role, (role) => role.users_roles, {})
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Field(() => [PermissionOverride])
  @OneToMany(() => PermissionOverride, (override) => override.user_role)
  permission_overrides: PermissionOverride[];
}
