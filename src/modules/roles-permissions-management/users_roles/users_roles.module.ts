import { Module } from '@nestjs/common';
import { UsersRolesService } from './users_roles.service';
import { UsersRolesResolver } from './users_roles.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserRole } from './entities/user_role.entity';

@Module({
  imports: [TypeOrmModule.forFeature([UserRole])],
  providers: [UsersRolesResolver, UsersRolesService],
  exports: [UsersRolesService],
})
export class UsersRolesModule {}
