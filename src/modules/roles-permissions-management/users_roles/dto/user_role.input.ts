import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';

@InputType()
export class InsertUserRoleInput {
  @Field(() => String)
  @IsString()
  user_id: string;

  @Field(() => String)
  @IsString()
  role_id: string;

  @Field(() => Boolean, { nullable: true })
  @IsBoolean()
  @IsOptional()
  personalized?: boolean;
}

@InputType()
export class UpdateUserRoleInput extends PartialType(InsertUserRoleInput) {
  @IsUUID()
  @Field(() => String)
  id: string;
}
