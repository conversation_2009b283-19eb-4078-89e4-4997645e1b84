import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserRole } from './entities/user_role.entity';
import { Repository } from 'typeorm';
import { Permission } from '../permissions/entities/permission.entity';
import { EActionPermission } from 'src/common/enums/action-permission.enum';

@Injectable()
export class UsersRolesService {
  constructor(
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
  ) {}

  /**
   * Calcula la lista de permisos efectivos para una asignación de usuario-rol específica.
   *
   * Este es un método central del sistema de autorización que sigue estos pasos:
   * 1.  Obtiene todos los permisos base garantizados por el rol.
   * 2.  Obtiene todos los permisos personalizados (overrides) para esta asignación específica.
   * 3.  Aplica los overrides:
   *     - Si un override es de tipo 'REVOKE', elimina el permiso de la lista final.
   *     - Si un override es de tipo 'GRANT', añade el permiso a la lista final.
   * 4.  Devuelve una lista plana y única de los códigos de permiso resultantes.
   *
   * @param user_id El ID del usuario.
   * @param role_name El nombre del rol (ej: 'admin', 'user').
   * @returns Una promesa que resuelve a un array de strings con los códigos de permiso únicos.
   * @throws {NotFoundException} Si no se encuentra la asignación de rol para el usuario.
   */
  async getPermissionsByUserAndRole(
    user_id: string,
    role_name: string,
  ): Promise<string[]> {
    try {
      const userRole = await this.userRoleRepository
        .createQueryBuilder('userRole')
        .leftJoinAndSelect('userRole.role', 'role')
        .leftJoinAndSelect('role.role_permissions', 'rolePermission')
        .leftJoinAndSelect('rolePermission.permission', 'basePermission')
        .leftJoinAndSelect('userRole.permission_overrides', 'override')
        .leftJoinAndSelect('override.permission', 'overridePermission')
        .where('userRole.user_id = :user_id', { user_id })
        .andWhere('role.name = :role_name', { role_name })
        .getOne();

      if (!userRole) {
        throw new NotFoundException(
          `La asignación de rol '${role_name}' no fue encontrada para el usuario.`,
        );
      }

      const effectivePermissions = new Map<string, Permission>();
      userRole.role.role_permissions?.forEach((rp) => {
        if (rp.permission) {
          effectivePermissions.set(rp.permission.id, rp.permission);
        }
      });

      // APLICAR LOS OVERRIDES
      userRole.permission_overrides?.forEach((override) => {
        if (override.permission) {
          if (override.action === EActionPermission.REVOKE) {
            // Si la acción es quitar, eliminamos el permiso del Map.
            effectivePermissions.delete(override.permission.id);
          } else if (override.action === EActionPermission.GRANT) {
            // Si la acción es agregar, añadimos o sobreescribimos el permiso en el Map.
            effectivePermissions.set(
              override.permission.id,
              override.permission,
            );
          }
        }
      });

      return Array.from(effectivePermissions.values()).map(
        (permission) => permission.code,
      );
    } catch (error) {
      console.error('[DEBUG] Error en getPermissionsByUserAndRole:', error);
      throw error;
    }
  }
}
