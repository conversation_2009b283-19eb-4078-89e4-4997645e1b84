import { Module } from '@nestjs/common';
import { PermissionDependenciesService } from './permission_dependencies.service';
import { PermissionDependenciesResolver } from './permission_dependencies.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionDependency } from './entities/permission_dependency.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PermissionDependency])],
  providers: [PermissionDependenciesResolver, PermissionDependenciesService],
  exports: [PermissionDependenciesService],
})
export class PermissionDependenciesModule {}
