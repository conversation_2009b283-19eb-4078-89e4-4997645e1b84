import { ObjectType, Field } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import { AuditableEntity } from 'src/common/entities/auditable.entity'; // Asegúrate que la ruta es correcta
import { Permission } from 'src/modules/roles-permissions-management/permissions/entities/permission.entity';
import { Entity, PrimaryColumn, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity({ name: 'permission_dependencies' })
export class PermissionDependency extends AuditableEntity {
  @Field(() => String)
  @IsUUID()
  @PrimaryColumn({ type: 'uuid' })
  permission_id: string;

  @Field(() => String)
  @IsUUID()
  @PrimaryColumn({ type: 'uuid' })
  required_permission_id: string;

  // ==========================
  // >>> RELATIONS <<<
  // ==========================

  @Field(() => Permission)
  @ManyToOne(() => Permission, (permission) => permission.dependencies)
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;

  @Field(() => Permission)
  @ManyToOne(() => Permission, (permission) => permission.required_for)
  @JoinColumn({ name: 'required_permission_id' })
  required_permission: Permission;
}
