import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { PermissionDependenciesService } from './permission_dependencies.service';
import { PermissionDependency } from './entities/permission_dependency.entity';

@Resolver(() => PermissionDependency)
export class PermissionDependenciesResolver {
  constructor(
    private readonly permissionDependenciesService: PermissionDependenciesService,
  ) {}
}
