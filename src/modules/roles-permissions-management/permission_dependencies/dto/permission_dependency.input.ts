import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsOptional, IsUUID, Length } from 'class-validator';

@InputType()
export class InsertPermissionDependencyInput {
  @Field(() => String)
  @IsUUID()
  permission_id: string;

  @Field(() => String)
  @IsUUID()
  required_permission_id: string;
}

@InputType()
export class UpdatePermissionDependencyInput extends PartialType(
  InsertPermissionDependencyInput,
) {}
