import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Payload_Access, Payload_Refresh } from './interfaces/auth.interface';

@Injectable()
export class AuthService {
  constructor(private readonly jwtService: JwtService) {}

  /**
   * Crea y firma un Access Token.
   * Utiliza la configuración por defecto (secret y expiresIn) ya inyectada en el JwtService.
   * @param payload La información a incluir en el token.
   * @returns Una promesa que resuelve al token de acceso.
   */
  async createAccessToken(payload: Payload_Access): Promise<string> {
    return this.jwtService.sign(payload);
  }

  /**
   * Crea y firma un Refresh Token.
   * Utiliza un secret y un tiempo de expiración diferentes, leídos desde las variables de entorno.
   * @param payload La información a incluir en el token.
   * @returns Una promesa que resuelve al token de refresco.
   */
  async createRefreshToken(payload: Payload_Refresh): Promise<string> {
    const secret = process.env.JWT_SECRET_KEY_REFRESH;
    const expiresIn = process.env.JWT_EXPIRES_IN_REFRESH;

    return this.jwtService.sign(payload, { secret, expiresIn });
  }

  /**
   * Verifica la validez de un Refresh Token.
   * @param token El token a verificar.
   * @returns El payload del token si es válido.
   */
  async verifyRefreshToken(token: string): Promise<Payload_Refresh> {
    try {
      return await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY_REFRESH,
      });
    } catch (error) {
      console.error('Error al verificar el Refresh Token:', error.message);
      throw error;
    }
  }
}
