import { Response } from 'express';
import { HttpStatus } from '@nestjs/common';
import { CookieOptions } from 'express';
import { AuthOauthResponse } from '../interfaces/auth.interface';

/**
 * Define los nombres de las cookies para los diferentes tipos de usuario (cliente y admin).
 * Esto permite mantener sesiones separadas y seguras para cada frontend.
 */
export const tipo_cookie = [
  {
    tipo_user: 'user',
    name_cookie: 'refreshToken',
  },
  {
    tipo_user: 'admin',
    name_cookie: 'refreshTokenAdmin',
  },
];

/**
 * Elimina la cookie del refresh token del navegador y envía una respuesta de sesión cerrada.
 * @param res El objeto Response de Express para modificar las cookies y enviar la respuesta.
 * @param domain El dominio para el cual se debe eliminar la cookie.
 * @param is_user Booleano que determina si se elimina la cookie de cliente (true) o de admin (false).
 */
export function clearRefreshTokenCookie(
  res: Response,
  domain: string,
  is_user: boolean,
) {
  const name_cookie = is_user
    ? tipo_cookie[0].name_cookie
    : tipo_cookie[1].name_cookie;

  res.clearCookie(name_cookie, {
    domain,
    path: '/',
  });
  res.status(HttpStatus.OK).send({
    message: 'Sesión cerrada',
  });
}

/**
 * Establece la cookie HttpOnly segura con el refresh token y envía el access token en el cuerpo de la respuesta.
 * @param res El objeto Response de Express.
 * @param refresh_token El token JWT de larga duración.
 * @param access_token El token JWT de corta duración.
 * @param permissions Un array con los permisos del usuario.
 * @param domain El dominio al que se asignará la cookie.
 * @param is_user Booleano para diferenciar entre cookie de cliente (true) y de admin (false).
 */
export function setRefreshTokenCookie(
  res: Response,
  refresh_token: string,
  access_token: string,
  permissions: string[],
  domain: string,
  is_user: boolean,
) {
  const name_cookie = is_user
    ? tipo_cookie[0].name_cookie
    : tipo_cookie[1].name_cookie;

  res.cookie(name_cookie, refresh_token, options(domain));
  res.status(HttpStatus.OK).send({ accessToken: access_token, permissions });
}

/**
 * Genera la configuración estándar y segura para las cookies de la aplicación.
 * @param domain El dominio para el cual se configuran las opciones.
 * @returns Un objeto `CookieOptions` con la configuración de seguridad.
 */
export function options(domain: string): CookieOptions {
  return {
    httpOnly: true, // Impide acceso desde JavaScript (protección XSS)
    domain,
    secure: process.env.NODE_ENV === 'production', // Solo se envía sobre HTTPS en producción
    sameSite: 'strict', // Máxima protección contra ataques CSRF
    maxAge: getMaxAge(),
    path: '/',
  };
}

/**
 * Parsea el tiempo de expiración del refresh token desde las variables de entorno (ej: "7d", "1h").
 * Si no está definido o es inválido, devuelve un valor por defecto de 1 hora.
 * @returns El tiempo de vida máximo de la cookie en milisegundos.
 */
export function getMaxAge(): number {
  const DEFAULT_REFRESH_TIME = 3600000; // 1 hora en milisegundos
  const time = process.env.JWT_EXPIRES_IN_REFRESH;

  if (!time) return DEFAULT_REFRESH_TIME;

  const match = time.match(/(\d+)([smhwd])/i);
  if (!match) return DEFAULT_REFRESH_TIME;

  const num = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  const unitInMilliseconds = {
    s: 1000,
    m: 60000,
    h: 3600000,
    d: 86400000,
    w: 604800000,
  };

  if (!unitInMilliseconds[unit]) return DEFAULT_REFRESH_TIME;

  return num * unitInMilliseconds[unit];
}

/**
 * Extrae el valor de la cookie del refresh token (cliente o admin) del objeto de cookies de la solicitud.
 * @param cookies El objeto `req.cookies` que contiene todas las cookies.
 * @param is_user Booleano para especificar cuál cookie buscar (true para cliente, false para admin).
 * @returns El valor del refresh token como string, o undefined si no se encuentra.
 */
export function getRefreshTokenCookie(
  cookies: Record<string, string>,
  is_user: boolean = true,
): string {
  const name_cookie = is_user
    ? tipo_cookie[0].name_cookie
    : tipo_cookie[1].name_cookie;
  return cookies[name_cookie];
}

/**
 * Genera una página HTML simple para el callback de OAuth2.
 * Esta página envía los datos de autenticación (token y permisos) a la ventana principal
 * de la aplicación a través de `window.postMessage` y luego se cierra sola.
 * @param accessToken El token de acceso a enviar al frontend.
 * @param permissions Los permisos del usuario.
 * @returns Un string con el contenido HTML completo.
 */
export function getHtmlPostMessage(
  accessToken: string,
  permissions: string[],
): string {
  const authData: AuthOauthResponse = {
    status: 'authenticationComplete',
    token: accessToken,
    permissions: permissions,
  };
  return `
  <html>
    <body>
      <script>
        window.opener.postMessage(${JSON.stringify(authData)}, "${process.env.FRONTEND_URL_CLIENT}");
        window.close();
      </script>
    </body>
  </html>
`;
}
