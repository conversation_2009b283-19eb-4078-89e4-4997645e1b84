import { User } from 'src/modules/user/entities/user.entity';
import { Payload_Access, Payload_Refresh } from '../interfaces/auth.interface';
import { ERole } from 'src/common/enums/role.enum';

export async function createPayloads(
  user: User,
  codes_roles: string[],
  role_active: string,
  subscription_active: boolean,
): Promise<{
  payload_access: Payload_Access;
  payload_refresh: Payload_Refresh;
}> {
  const role = role_active || ERole.USUARIO!;
  const payload_access: Payload_Access = {
    sub: user.id,
    username: user.username || '',
    email: user.email || '',
    full_name: user.full_name || '',
    profile_picture: user.profile_picture || '',
    role_active: role,
    roles: codes_roles,
    subscription_active: subscription_active,
  };
  const payload_refresh: Payload_Refresh = { sub: user.id, role_active: role };
  return { payload_access, payload_refresh };
}
