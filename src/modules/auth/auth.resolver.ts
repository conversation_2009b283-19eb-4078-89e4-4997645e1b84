import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { Auth } from './entities/auth.entity';
import { Public } from 'src/common/decorators/public.decorator';
import { Payload_Access } from './interfaces/auth.interface';
import { CredentialsService } from '../credentials/credentials.service';

@Resolver(() => Auth)
export class AuthResolver {
  constructor(
    private readonly authService: AuthService,
    private readonly credentialsService: CredentialsService,
  ) {}
}
