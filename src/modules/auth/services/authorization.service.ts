import { Injectable, Logger } from '@nestjs/common';
import { UsersRolesService } from 'src/modules/roles-permissions-management/users_roles/users_roles.service';

@Injectable()
export class AuthorizationService {
  private readonly logger = new Logger(AuthorizationService.name);

  constructor(private readonly userRolesService: UsersRolesService) {}

  /**
   * Calcula y devuelve la lista final de códigos de permiso efectivos para un usuario y su rol activo.
   * Este servicio actúa como un orquestador, delegando la lógica de cálculo al UsersRolesService
   * y manejando los logs y errores de forma centralizada.
   *
   * @param userId - El ID del usuario.
   * @param activeRole - El nombre del rol activo.
   * @returns Una promesa que resuelve a un array de strings con los códigos de permiso efectivos.
   */
  async getEffectivePermissionsForUser(
    userId: string,
    activeRole: string,
  ): Promise<string[]> {
    // this.logger.debug(
    //   `Calculando permisos efectivos para UserID: ${userId}, Rol: ${activeRole}`,
    // );

    try {
      const permissionCodes: string[] =
        await this.userRolesService.getPermissionsByUserAndRole(
          userId,
          activeRole,
        );

      // this.logger.debug(
      //   `Permisos efectivos encontrados: [${permissionCodes.join(', ')}]`,
      // );
      return permissionCodes;
    } catch (error) {
      this.logger.error(
        `Error al calcular permisos para el usuario ${userId} con rol ${activeRole}: ${error.message}`,
        error.stack,
      );

      return [];
    }
  }
}
