import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  Req,
  Res,
  UnauthorizedException,
  UseFilters,
  UseGuards,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { CredentialsService } from '../credentials/credentials.service';
import { Public } from 'src/common/decorators/public.decorator';
import {
  clearRefreshTokenCookie,
  getRefreshTokenCookie,
  setRefreshTokenCookie,
} from './utils/cookie.util';
import { CreateAccountDto } from '../credentials/dto/credential.input';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly credentialsService: CredentialsService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * <PERSON><PERSON> las credenciales (username/contraseña) para iniciar sesión.
   * Si la autenticación es exitosa, genera los tokens y establece una cookie
   * HttpOnly segura con el refresh token para manejar la sesión.
   *
   * @route POST /auth/login
   */
  @Public()
  @Post('login')
  async loginWithUsernameAndPassword(
    @Body('username') username: string,
    @Body('password') password: string,
    @Res({ passthrough: true }) res: Response,
  ): Promise<void> {
    console.log('Intento de logueo');
    const { payload_access, payload_refresh, permissions } =
      await this.credentialsService.loginWithUsernameAndPassword(
        username,
        password,
      );

    const accessToken = `Bearer ${await this.authService.createAccessToken(payload_access)}`;
    const refreshToken = `Bearer ${await this.authService.createRefreshToken(payload_refresh)}`;

    setRefreshTokenCookie(
      res,
      refreshToken,
      accessToken,
      permissions,
      process.env.FRONTEND_DOMAIN_CLIENT!,
      true,
    );
  }

  /**
   * Valida las credenciales (username/contraseña) de un usuario administrador.
   * Si la autenticación es exitosa, genera los tokens y establece una cookie
   * HttpOnly segura específica para el dominio del panel de administración.
   *
   * @route POST /auth/login-admin
   */
  @Public()
  @Post('login-admin')
  async loginWithUsernameAndPasswordAdmin(
    @Body('username') username: string,
    @Body('password') password: string,
    @Res({ passthrough: true }) res: Response,
  ): Promise<void> {
    const { payload_access, payload_refresh, permissions } =
      await this.credentialsService.loginWithUsernameAndPasswordAdmin(
        username,
        password,
      );

    const accessToken = `Bearer ${await this.authService.createAccessToken(payload_access)}`;
    const refreshToken = `Bearer ${await this.authService.createRefreshToken(payload_refresh)}`;

    setRefreshTokenCookie(
      res,
      refreshToken,
      accessToken,
      permissions,
      process.env.FRONTEND_DOMAIN_ADMIN!,
      false,
    );
  }

  /**
   * Genera un nuevo access token para un usuario cliente utilizando el refresh token.
   * Este endpoint permite mantener la sesión activa sin requerir que el usuario
   * vuelva a introducir sus credenciales.
   *
   * @route POST /auth/refresh-access-token
   */
  @Public()
  @Post('refresh-access-token')
  async refreshToken(
    @Req() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<void> {
    let refreshTokenOld = req.cookies.refreshToken;
    if (!refreshTokenOld) return;

    refreshTokenOld = refreshTokenOld.replace('Bearer ', '');

    const { payload_access, payload_refresh, permissions } =
      await this.credentialsService.refreshAccessToken(refreshTokenOld);

    const accessToken = `Bearer ${await this.authService.createAccessToken(payload_access)}`;
    const refreshToken = `Bearer ${await this.authService.createRefreshToken(payload_refresh)}`;

    setRefreshTokenCookie(
      res,
      refreshToken,
      accessToken,
      permissions,
      process.env.FRONTEND_DOMAIN_CLIENT!,
      true,
    );
  }

  /**
   * Genera un nuevo access token para un usuario administrador.
   * Funciona de la misma manera que el refresh para clientes, pero utiliza
   * la cookie y el dominio específicos del panel de administración.
   *
   * @route POST /auth/refresh-access-token-admin
   */
  @Public()
  @Post('refresh-access-token-admin')
  async refreshTokenAdmin(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
  ): Promise<void> {
    let refreshTokenOld = req.cookies.refreshTokenAdmin;
    if (!refreshTokenOld) {
      throw new UnauthorizedException('Refresh token not found');
    }

    refreshTokenOld = refreshTokenOld.replace('Bearer ', '');

    const { payload_access, payload_refresh, permissions } =
      await this.credentialsService.refreshAccessToken(refreshTokenOld);

    const accessToken = `Bearer ${await this.authService.createAccessToken(payload_access)}`;
    const refreshToken = `Bearer ${await this.authService.createRefreshToken(payload_refresh)}`;

    setRefreshTokenCookie(
      res,
      refreshToken,
      accessToken,
      permissions,
      process.env.FRONTEND_DOMAIN_ADMIN!,
      false,
    );
  }

  /**
   * Cierra la sesión de un usuario cliente.
   * Invalida la sesión eliminando la cookie del refresh token del navegador.
   *
   * @route POST /auth/remove-refresh-token
   */
  @Public()
  @Post('remove-refresh-token')
  async removeRefreshToken(
    @Res({ passthrough: true }) res: Response,
  ): Promise<void> {
    clearRefreshTokenCookie(res, process.env.FRONTEND_DOMAIN_CLIENT!, true);
  }

  /**
   * Cierra la sesión de un usuario administrador.
   * Invalida la sesión eliminando la cookie del refresh token específica del admin.
   *
   * @route POST /auth/remove-refresh-token-admin
   */
  @Public()
  @Post('remove-refresh-token-admin')
  async removeRefreshTokenAdmin(
    @Res({ passthrough: true }) res: Response,
  ): Promise<void> {
    clearRefreshTokenCookie(res, process.env.FRONTEND_DOMAIN_ADMIN!, false);
  }

  /**
   * Permite a un usuario administrador cambiar su rol activo.
   * Al cambiar de rol, se regeneran los tokens de acceso y refresco
   * con los permisos correspondientes al nuevo rol seleccionado.
   *
   * @route POST /auth/change-role
   */
  @Post('change-role')
  async changeRole(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
  ) {
    // Obtiene la cookie del admin. 'false' indica que no es la del cliente.
    let refresh = getRefreshTokenCookie(req.cookies, false);
    if (!refresh) {
      throw new UnauthorizedException('Refresh token not found');
    }

    refresh = refresh.replace('Bearer ', '');
    const change_role = req.body.role_active;

    const { payload_access, payload_refresh, permissions } =
      await this.credentialsService.changeRole(refresh, change_role);

    const accessToken = `Bearer ${await this.authService.createAccessToken(payload_access)}`;
    const refreshToken = `Bearer ${await this.authService.createRefreshToken(payload_refresh)}`;

    setRefreshTokenCookie(
      res,
      refreshToken,
      accessToken,
      permissions,
      process.env.FRONTEND_DOMAIN_ADMIN!,
      false,
    );
  }

  // /**
  //  * Crea una nueva cuenta de usuario.
  //  * Endpoint público utilizado para el registro de nuevos usuarios en la plataforma.
  //  *
  //  * @route POST /auth/create-account
  //  */
  // @Public()
  // @Post('create-account')
  // async createAccountUser(
  //   @Body() create_account_dto: CreateAccountDto,
  // ): Promise<boolean> {
  //   return await this.credentialsService.createAccountUser(create_account_dto);
  // }
}
