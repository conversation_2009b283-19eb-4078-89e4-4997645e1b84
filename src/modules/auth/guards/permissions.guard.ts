import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';
import { AuthorizationService } from '../services/authorization.service';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly authorizationService: AuthorizationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // Acceso concedido si no se requieren permisos
    }

    const gqlContext = GqlExecutionContext.create(context);
    const { req } = gqlContext.getContext();
    const user = req.user;

    if (!user) {
      return false;
    }

    const userPermissions =
      await this.authorizationService.getEffectivePermissionsForUser(
        user.sub,
        user.role_active,
      );

    const userPermissionsSet = new Set(userPermissions);

    const hasAllPermissions = requiredPermissions.every((permission) =>
      userPermissionsSet.has(permission),
    );

    if (hasAllPermissions) {
      return true;
    }

    throw new ForbiddenException(
      'No tienes los permisos necesarios para realizar esta acción.',
    );
  }
}
