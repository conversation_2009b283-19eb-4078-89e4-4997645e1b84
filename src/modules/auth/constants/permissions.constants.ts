export const PERMISSIONS = {
  SIDEBAR: {
    VIEW_CAMPAIGNS: 'sidebar:view-campaigns',
    VIEW_ROLES_AND_PERMISSIONS: 'sidebar:view-roles-and-permissions',
    VIEW_REPORTS: 'sidebar:view-reports',

    // PENDIENTE: Registrar en la base de datos.
    VIEW_USERS: 'sidebar:view-users',
  },
  ROUTE: {
    ACCESS_CAMPAIGNS: 'route:access-campaigns',
    ACCESS_ROLES_AND_PERMISSIONS: 'route:access-roles-and-permissions',
    ACCESS_REPORTS: 'route:access-reports',

    // PENDIENTE: Registrar los siguientes permisos en la base de datos.
    ACCESS_USERS: 'route:access-users',
  },
  API: {
    CAMPAIGNS: {
      READ: 'campaigns:read',
      CREATE: 'campaigns:create',
      UPDATE: 'campaigns:update',
      DELETE: 'campaigns:delete',
    },
    // PENDIENTE: Registrar los permisos de ROLES en la base de datos.
    ROLES: {
      READ: 'roles:read',
      CREATE: 'roles:create',
      UPDATE: 'roles:update',
      DELETE: 'roles:delete',
    },
    PERMISSIONS: {
      READ: 'permissions:read',
    },
    ASSIGNMENTS: {
      UPDATE: 'assignments:update',
    },
  },
} as const;
