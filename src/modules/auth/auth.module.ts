import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule, ConfigType } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';

import { AuthService } from './auth.service';
import { AuthResolver } from './auth.resolver';
import { JwtStrategy } from './strategies/jwt.strategy';
import jwtConfig from './config/jwt.config';
import { AuthController } from './auth.controller';
import { CredentialsModule } from '../credentials/credentials.module';
import { UserModule } from '../user/user.module';
import { AuthorizationService } from './services/authorization.service';
import { UsersRolesModule } from '../roles-permissions-management/users_roles/users_roles.module';

@Module({
  controllers: [AuthController],
  providers: [AuthService, AuthResolver, JwtStrategy, AuthorizationService],
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    ConfigModule.forFeature(jwtConfig),
    JwtModule.registerAsync({
      imports: [ConfigModule.forFeature(jwtConfig)],
      inject: [jwtConfig.KEY],
      useFactory: (config: ConfigType<typeof jwtConfig>) => ({
        secret: config.secret,
        signOptions: { expiresIn: config.signOptions?.expiresIn },
      }),
    }),

    forwardRef(() => CredentialsModule),
    UserModule,
    UsersRolesModule,
  ],
  exports: [AuthService, JwtModule, AuthorizationService],
})
export class AuthModule {}
