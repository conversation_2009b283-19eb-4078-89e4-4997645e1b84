import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import jwtConfig from '../config/jwt.config';
import { Payload_Access } from '../interfaces/auth.interface';
import { UserService } from 'src/modules/user/user.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    @Inject(jwtConfig.KEY)
    private jwtConfiguration: ConfigType<typeof jwtConfig>,
    private readonly userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: jwtConfiguration.secret!,
      ignoreExpiration: false,
    });
  }

  /**
   * Este método se ejecuta DESPUÉS de que Passport verifica la firma y expiración del token.
   * Su propósito es recibir el payload y permitirnos hacer validaciones adicionales.
   * @param payload El payload decodificado del JWT.
   * @returns Lo que retornemos aquí, NestJS lo adjuntará al objeto `request` como `request.user`.
   */
  async validate(payload: Payload_Access) {
    //Nota: ver si es necesario cambiar el tipo de payload
    const user = await this.userService.findUser_ById(payload.sub);
    if (!user) {
      throw new UnauthorizedException('El usuario del token ya no existe.');
    }

    return payload;
  }
}
