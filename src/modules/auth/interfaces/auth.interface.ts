/**
 * Define la estructura de datos (payload) que se almacena
 * dentro del Access Token. Esta información estará disponible
 * en cada petición a una ruta protegida.
 */
export interface Payload_Access {
  sub: string;
  username: string;
  email: string;
  full_name: string;
  profile_picture: string;
  subscription_active: boolean;
  role_active: string;
  roles: string[];
}

/**
 * Define el payload para el Refresh Token. Generalmente es más minimalista,
 * solo necesita lo esencial para identificar al usuario y emitir un nuevo access token.
 */
export interface Payload_Refresh {
  sub: string;
  role_active: string;
}

/**
 * Define la forma de la respuesta que se envía al cliente
 * después de un inicio de sesión exitoso.
 */
export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  permissions: string[];
}

export interface AuthOauthResponse {
  status: 'authenticationComplete' | 'authenticationIncomplete';
  token: string;
  permissions: string[];
}
