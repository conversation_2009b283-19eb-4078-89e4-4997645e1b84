import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CandidateService } from './candidate.service';
import { Candidate } from './entities/candidate.entity';
import { CreateCandidateInput } from './dto/create-candidate.input';
import { UpdateCandidateInput } from './dto/update-candidate.input';

@Resolver()
export class CandidateResolver {
  constructor(private readonly candidateService: CandidateService) {}

  @Query(() => [Candidate])
  async getCandidates(): Promise<Candidate[]> {
    return this.candidateService.findAll();
  }

  @Query(() => Candidate)
  async candidate(@Args('id') id: string): Promise<Candidate> {
    return this.candidateService.findOne(id);
  }

  @Mutation(() => Candidate)
  async createCandidate(
    @Args('createCandidateInput') createCandidateInput: CreateCandidateInput,
  ): Promise<Candidate> {
    return this.candidateService.create(createCandidateInput);
  }

  @Mutation(() => Candidate)
  async updateCandidate(
    @Args('updateCandidateInput') updateCandidateInput: UpdateCandidateInput,
  ): Promise<Candidate> {
    return await this.candidateService.update(updateCandidateInput);
  }

  @Mutation(() => Candidate)
  async deleteCandidate(@Args('id') id: string): Promise<Candidate> {
    return await this.candidateService.remove(id);
  }
}
