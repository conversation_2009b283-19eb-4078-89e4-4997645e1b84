import { Field, ObjectType } from '@nestjs/graphql';
import { Campaign } from 'src/modules/campaign/entities/campaign.entity';
import { PoliticalParty } from 'src/modules/political-party/entities/political-party.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

@ObjectType()
@Entity({ name: 'candidates' })
export class Candidate {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String)
  id: string;

  @Column({ length: 100 })
  @Field(() => String)
  full_name: string;

  @Column()
  @Field(() => String, { nullable: true })
  political_party_id: string;

  @Field(() => PoliticalParty, { nullable: true })
  @ManyToOne(
    () => PoliticalParty,
    (politicalParty) => politicalParty.candidates,
  )
  @JoinColumn({ name: 'political_party_id' })
  political_party?: PoliticalParty;

  // @Field(()=> [Campaign], { nullable: true })
  // @OneToMany(() => Campaign, (campaign) => campaign.candidate)
  // campaigns?: Campaign[];
}
