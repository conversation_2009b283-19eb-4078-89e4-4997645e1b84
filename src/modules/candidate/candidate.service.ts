import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Candidate } from './entities/candidate.entity';
import { CreateCandidateInput } from './dto/create-candidate.input';
import { UpdateCandidateInput } from './dto/update-candidate.input';

@Injectable()
export class CandidateService {
  constructor(
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
  ) {}

  async create(createCandidateInput: CreateCandidateInput): Promise<Candidate> {
    const candidate = this.candidateRepository.create(createCandidateInput);
    return await this.candidateRepository.save(candidate);
  }

  async findAll(): Promise<Candidate[]> {
    return await this.candidateRepository.find({
      relations: ['political_party', 'campaigns'],
    });
  }

  async findOne(id: string): Promise<Candidate> {
    const candidate = await this.candidateRepository.findOne({
      where: { id },
      relations: ['political_party', 'campaigns'],
    });
    if (!candidate) {
      throw new NotFoundException(`Candidate with ID ${id} not found`);
    }
    return candidate;
  }

  async update(updateCandidateInput: UpdateCandidateInput): Promise<Candidate> {
    const candidate = await this.findOne(updateCandidateInput.id);
    
    Object.assign(candidate, updateCandidateInput);
    
    return await this.candidateRepository.save(candidate);
  }

  async remove(id: string): Promise<Candidate> {
    const candidate = await this.findOne(id);
    await this.candidateRepository.remove(candidate);
    return candidate;
  }

  async findByPoliticalParty(politicalPartyId: string): Promise<Candidate[]> {
    return await this.candidateRepository.find({
      where: { political_party_id: politicalPartyId },
      relations: ['political_party', 'campaigns'],
    });
  }

  async findByName(fullName: string): Promise<Candidate[]> {
    return await this.candidateRepository.find({
      where: { full_name: fullName },
      relations: ['political_party', 'campaigns'],
    });
  }
}
