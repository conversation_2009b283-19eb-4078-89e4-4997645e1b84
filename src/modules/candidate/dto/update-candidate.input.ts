import { Field, InputType, PartialType } from '@nestjs/graphql';
import { CreateCandidateInput } from './create-candidate.input';

@InputType()
export class UpdateCandidateInput extends PartialType(CreateCandidateInput) {
    
    @Field(() => String)
    id: string;
  
    @Field(() => String, { nullable: true })
    full_name: string;
  
    @Field(() => String, { nullable: true })
    political_party_id: string;

} 