import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Campaign } from './entities/campaign.entity';
import { IsNull, Repository } from 'typeorm';
import { CreateCampaignInput } from './dto/create-campaign.input';
import { UpdateCampaignInput } from './dto/update-campaign.input';
import { EncryptionService } from 'src/encryption/encryption.service';

@Injectable()
export class CampaignService {
  constructor(
    @InjectRepository(Campaign)
    private campaignRepository: Repository<Campaign>,
    private readonly encryptionService: EncryptionService,
  ) {}

  async getCampaignByTenantId(tenantId: string): Promise<Campaign | null> {
    return this.campaignRepository.findOne({
      where: { wati_tenant_id: tenantId },
    });
  }

  async createCampaign(createCampaignInput: CreateCampaignInput) {

    console.log('antes',createCampaignInput);
    createCampaignInput.token = this.encryptionService.encrypt(createCampaignInput.token);
    console.log('despues',createCampaignInput);
    const campaignEntity = this.campaignRepository.create(createCampaignInput);
    return this.campaignRepository.save(campaignEntity);
  }

  async updateCampaign(updateCampaignInput: UpdateCampaignInput) {
    const campaignEntity = await this.campaignRepository.findOne({
      where: { id: updateCampaignInput.id, deleted_at: IsNull() },
    });
    if (!campaignEntity) {
      throw new NotFoundException('Campaign not found');
    }
    return this.campaignRepository.save(campaignEntity);
  }
}
