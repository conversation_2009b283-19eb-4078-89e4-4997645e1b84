import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Campaign } from './entities/campaign.entity';
import { IsNull, Repository } from 'typeorm';
import { CreateCampaignInput } from './dto/create-campaign.input';
import { UpdateCampaignInput } from './dto/update-campaign.input';
import { EncryptionService } from 'src/encryption/encryption.service';
import { PaginationInput } from 'src/common/dto/pagination.input';
import { CampaignPaginatedResponse } from './dto/campaign-paginated.response';

@Injectable()
export class CampaignService {
  constructor(
    @InjectRepository(Campaign)
    private campaignRepository: Repository<Campaign>,
    private readonly encryptionService: EncryptionService,
  ) {}

  async getCampaignByTenantId(tenantId: string): Promise<Campaign | null> {
    return this.campaignRepository.findOne({
      where: { wati_tenant_id: tenantId },
    });
  }

  async createCampaign(createCampaignInput: CreateCampaignInput) {
    console.log('antes', createCampaignInput);
    createCampaignInput.token = this.encryptionService.encrypt(
      createCampaignInput.token,
    );
    console.log('despues', createCampaignInput);
    const campaignEntity = this.campaignRepository.create(createCampaignInput);
    return this.campaignRepository.save(campaignEntity);
  }

  async updateCampaign(updateCampaignInput: UpdateCampaignInput) {
    const campaignEntity = await this.campaignRepository.findOne({
      where: { id: updateCampaignInput.id, deleted_at: IsNull() },
    });
    if (!campaignEntity) {
      throw new NotFoundException('Campaign not found');
    }
    return this.campaignRepository.save(campaignEntity);
  }

  async findPaginated(
    search: string,
    paginationInput: PaginationInput,
  ): Promise<CampaignPaginatedResponse> {
    const query = this.campaignRepository
      .createQueryBuilder('campaign')
      .where('campaign.deleted_at IS NULL');

    if (search) {
      const searchLower = `%${search.toLowerCase()}%`;
      query.andWhere('LOWER(campaign.name) LIKE :search', {
        search: searchLower,
      });
    }

    query.orderBy('campaign.created_at', 'DESC');

    const total = await query.getCount();

    const page = paginationInput?.page || 1;
    const limit = paginationInput?.limit || 10;
    const skip = (page - 1) * limit;

    const data = await query.skip(skip).take(limit).getMany();

    return { data, total };
  }
}
