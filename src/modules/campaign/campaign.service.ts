import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Campaign } from './entities/campaign.entity';
import { IsNull, Repository } from 'typeorm';
import { CreateCampaignInput } from './dto/create-campaign.input';
import { UpdateCampaignInput } from './dto/update-campaign.input';
import { EncryptionService } from 'src/encryption/encryption.service';
import { DatabaseManagerService } from 'src/database-manager/database-manager.service';
import { PaginationInput } from 'src/common/dto/pagination.input';
import { CampaignPaginatedResponse } from './dto/campaign-paginated.response';
import { CampaignUser } from './modules/campaign-users/entities/campaign-user.entity';

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name)

  constructor(
    @InjectRepository(Campaign)
    private campaignRepository: Repository<Campaign>,
    private readonly encryptionService: EncryptionService,
    private databaseManagerService: DatabaseManagerService,
  ) {}


  /**
   * Ejemplo: Obtener datos de una campaña específica
   */
  async getCampaignData(campaignId: string):Promise<void> {
    try {
      // Obtenemos la conexión a la DB de la campaña
      const campaignConnection = await this.databaseManagerService.getCampaignConnection(campaignId);
      
      // Ahora podemos usar esta conexión para acceder a los datos específicos de la campaña
      // Por ejemplo, obtener usuarios de la campaña:
      const userRepository = campaignConnection.getRepository(CampaignUser);
      const users = await userRepository.find();
      console.log(users);
      return;
    } catch (error) {
      this.logger.error(`Error obteniendo datos de campaña ${campaignId}:`, error);
      throw error;
    }
  }


  async getCampaignByTenantId(tenantId: string): Promise<Campaign | null> {
    return this.campaignRepository.findOne({
      where: { wati_tenant_id: tenantId },
    });
  }

  async createCampaign(createCampaignInput: CreateCampaignInput) {
    console.log('antes', createCampaignInput);
    createCampaignInput.token = this.encryptionService.encrypt(
      createCampaignInput.token,
    );
    console.log('despues', createCampaignInput);
    const campaignEntity = this.campaignRepository.create(createCampaignInput);
    return this.campaignRepository.save(campaignEntity);
  }

  async updateCampaign(updateCampaignInput: UpdateCampaignInput) {
    const campaignEntity = await this.campaignRepository.findOne({
      where: { id: updateCampaignInput.id, deleted_at: IsNull() },
    });
    if (!campaignEntity) {
      throw new NotFoundException('Campaign not found');
    }
    return this.campaignRepository.save(campaignEntity);
  }

  async findPaginated(
    search: string,
    paginationInput: PaginationInput,
  ): Promise<CampaignPaginatedResponse> {
    const query = this.campaignRepository
      .createQueryBuilder('campaign')
      .where('campaign.deleted_at IS NULL');

    if (search) {
      const searchLower = `%${search.toLowerCase()}%`;
      query.andWhere('LOWER(campaign.name) LIKE :search', {
        search: searchLower,
      });
    }

    query.orderBy('campaign.created_at', 'DESC');

    const total = await query.getCount();

    const page = paginationInput?.page || 1;
    const limit = paginationInput?.limit || 10;
    const skip = (page - 1) * limit;

    const data = await query.skip(skip).take(limit).getMany();

    return { data, total };
  }
}
