import { Module } from '@nestjs/common';
import { CampaignResolver } from './campaign.resolver';
import { CampaignService } from './campaign.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Campaign } from './entities/campaign.entity';
import { ConfigModule } from '@nestjs/config';
import { EncryptionModule } from 'src/encryption/encryption.module';
import { SystemUsersModule } from './modules/system-users/system-users.module';
import { ModulesService } from './modules/modules.service';
import { ModulesResolver } from './modules/modules.resolver';
import { CampaignUsersModule } from './modules/campaign-users/campaign-users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Campaign]),
    EncryptionModule,
    SystemUsersModule,
    CampaignUsersModule,
  ],
  providers: [CampaignResolver, CampaignService, ModulesService, ModulesResolver],
  exports: [CampaignService],
})
export class CampaignModule {}
