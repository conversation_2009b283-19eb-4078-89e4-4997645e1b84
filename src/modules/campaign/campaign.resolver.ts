import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CampaignService } from './campaign.service';
import { Campaign } from './entities/campaign.entity';
import { CreateCampaignInput } from './dto/create-campaign.input';
import { UpdateCampaignInput } from './dto/update-campaign.input';
import { CampaignPaginatedResponse } from './dto/campaign-paginated.response';
import { PaginationInput } from 'src/common/dto/pagination.input';
import { Permissions } from '../auth/decorators/permissions.decorator';
import { PERMISSIONS } from '../auth/constants/permissions.constants';
import { Public } from 'src/common/decorators/public.decorator';

@Resolver()
export class CampaignResolver {
  constructor(private readonly campaignService: CampaignService) {}

  @Query(() => CampaignPaginatedResponse)
  @Permissions(PERMISSIONS.API.CAMPAIGNS.READ)
  async findPaginatedCampaigns(
    @Args('search', { type: () => String, nullable: true })
    search: string,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput: PaginationInput,
  ): Promise<CampaignPaginatedResponse> {
    return await this.campaignService.findPaginated(search, paginationInput);
  }

    @Public()
  @Mutation(() => Campaign)
  async createCampaign(
    @Args('createCampaignInput') createCampaignInput: CreateCampaignInput,
  ): Promise<Campaign> {
    return await this.campaignService.createCampaign(createCampaignInput);
  }

  @Mutation(() => Campaign)
  async updateCampaign(
    @Args('updateCampaignInput') updateCampaignInput: UpdateCampaignInput,
  ): Promise<Campaign> {
    return await this.campaignService.updateCampaign(updateCampaignInput);
    }

    @Public()
    @Query(() => Boolean)
    async getCampaignData(@Args('campaignId') campaignId: string): Promise<boolean> {
        await this.campaignService.getCampaignData(campaignId);
        return true;
  }   
}
