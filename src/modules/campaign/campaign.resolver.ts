import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { CampaignService } from './campaign.service';
import { Campaign } from './entities/campaign.entity';
import { CreateCampaignInput } from './dto/create-campaign.input';
import { UpdateCampaignInput } from './dto/update-campaign.input';

@Resolver()
export class CampaignResolver {

    constructor(private readonly campaignService: CampaignService) {}

    @Mutation(() => Campaign)
    async createCampaign(@Args('createCampaignInput') createCampaignInput: CreateCampaignInput): Promise<Campaign> {
        return await this.campaignService.createCampaign(createCampaignInput);
    }

    @Mutation(() => Campaign)
    async updateCampaign(@Args('updateCampaignInput') updateCampaignInput: UpdateCampaignInput): Promise<Campaign> {
        return await this.campaignService.updateCampaign(updateCampaignInput);
    }

}
