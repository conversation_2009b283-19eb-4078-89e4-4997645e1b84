import { Field, ObjectType, Int } from '@nestjs/graphql';
import { Campaign } from '../entities/campaign.entity';

@ObjectType()
export class CampaignPaginatedResponse {
  @Field(() => [Campaign], {
    description: 'Lista de campañas para la página actual',
  })
  data: Campaign[];

  @Field(() => Int, {
    description: 'Número total de campañas que coinciden con la búsqueda',
  })
  total: number;
}
