import { Field, InputType } from "@nestjs/graphql";

@InputType()
export class CreateCampaignInput {

  @Field(() => String)
  name: string;

  @Field(() => String)
  candidate_id?: string;

  @Field(() => String )
  whatsapp_number: string;

  @Field(() => String)
  wati_tenant_id?: string;

  @Field(() => Boolean)
  is_active: boolean;

  @Field(() => Date, { nullable: true })
  start_date?: Date;

  @Field(() => Date, { nullable: true })
  end_date?: Date;

  @Field(() => String, { nullable: false })
  token: string;

}
