import { Field, ObjectType } from '@nestjs/graphql';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { CampaignRegistration } from 'src/modules/campaign-registrations/entities/campaign-registration.entity';
import { Candidate } from 'src/modules/candidate/entities/candidate.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

@ObjectType()
@Entity({ name: 'campaigns' })
export class Campaign extends AuditableEntity {
  @Field(() => String)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field(() => String)
  @Column({ length: 100 })
  name: string;

  @Field(() => Candidate, { nullable: true })
  @ManyToOne(() => Candidate, (candidate) => candidate.campaigns)
  @JoinColumn({ name: 'candidate_id' })
  candidate?: Candidate;

  @Column({ nullable: true })
  candidate_id?: string;

  @Field(() => String, { nullable: true })
  @Column({ length: 20, unique: true, nullable: true })
  whatsapp_number: string;

  @Field(() => String, { nullable: true })
  @Column({ length: 100, unique: true, nullable: true })
  wati_tenant_id?: string;

  @Field()
  @Column({ default: false })
  is_active: boolean;

  @Field({ nullable: true })
  @Column({ type: 'date', nullable: true })
  start_date?: Date;

  @Field({ nullable: true })
  @Column({ type: 'date', nullable: true })
  end_date?: Date;

  @Field(() => [CampaignRegistration], { nullable: true })
  @OneToMany(
    () => CampaignRegistration,
    (campaignRegistration) => campaignRegistration.campaign,
  )
  campaign_registrations?: CampaignRegistration[];

  // Token sensible de integración (no se expone por GraphQL)
  @Column({ type: 'text', nullable: true })
  token?: string;
}
