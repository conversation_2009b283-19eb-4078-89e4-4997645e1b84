import { Test, TestingModule } from '@nestjs/testing';
import { CampaignUsersResolver } from './campaign-users.resolver';

describe('CampaignUsersResolver', () => {
  let resolver: CampaignUsersResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CampaignUsersResolver],
    }).compile();

    resolver = module.get<CampaignUsersResolver>(CampaignUsersResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
