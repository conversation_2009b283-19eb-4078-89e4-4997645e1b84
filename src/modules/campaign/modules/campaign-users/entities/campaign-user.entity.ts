import { AuditableEntity } from "src/common/entities/auditable.entity";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

// CREATE TABLE users (
//   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
//   email VARCHAR(255) UNIQUE NOT NULL,
//   name VARCHAR(255) NOT NULL,
// );

@Entity({ name: 'campaign_users' })
export class CampaignUser extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;
}