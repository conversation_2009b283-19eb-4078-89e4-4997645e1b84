import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PoliticalParty } from './entities/political-party.entity';
import { Repository } from 'typeorm';
import { InsertPoliticalPartyInput } from './dto/insert-political-party.input';
import { UpdatePoliticalPartyInput } from './dto/update-political-party.input';

@Injectable()
export class PoliticalPartyService {

    constructor(
        @InjectRepository(PoliticalParty)
        private readonly politicalPartyRepository: Repository<PoliticalParty>,
    ) {}

    async findAll(): Promise<PoliticalParty[]> {
        return await this.politicalPartyRepository.find();
    }

    async findById(id: string): Promise<PoliticalParty> {
        const politicalParty = await this.politicalPartyRepository.findOne({ 
            where: { id } 
        });
        
        if (!politicalParty) {
            throw new NotFoundException(`Political party with ID ${id} not found`);
        }
        
        return politicalParty;
    }

    async create(politicalParty: InsertPoliticalPartyInput): Promise<PoliticalParty> {
        const newPoliticalParty = this.politicalPartyRepository.create({
            name: politicalParty.name,
            short_name: politicalParty.short_name,
        });
        
        return await this.politicalPartyRepository.save(newPoliticalParty);
    }

    async update(id: string, politicalParty: UpdatePoliticalPartyInput): Promise<PoliticalParty> {
        const existingParty = await this.findById(id);
        
        // Only update fields that are provided
        if (politicalParty.name !== undefined) {
            existingParty.name = politicalParty.name;
        }
        if (politicalParty.short_name !== undefined) {
            existingParty.short_name = politicalParty.short_name;
        }
        
        return await this.politicalPartyRepository.save(existingParty);
    }

    async delete(id: string): Promise<boolean> {
        const existingParty = await this.findById(id);
        await this.politicalPartyRepository.remove(existingParty);
        return true;
    }
}
