import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { PoliticalPartyService } from './political-party.service';
import { PoliticalParty } from './entities/political-party.entity';
import { InsertPoliticalPartyInput } from './dto/insert-political-party.input';
import { UpdatePoliticalPartyInput } from './dto/update-political-party.input';

@Resolver(() => PoliticalParty)
export class PoliticalPartyResolver {

    constructor(
        private readonly politicalPartyService: PoliticalPartyService,
    ) {}

    @Query(() => [PoliticalParty], { 
        description: 'Get all political parties',
        nullable: true 
    })
    async getPoliticalParties(): Promise<PoliticalParty[]> {
        try {
            return await this.politicalPartyService.findAll();
        } catch (error) {
            throw new Error(`Failed to fetch political parties: ${error.message}`);
        }
    }

    @Query(() => PoliticalParty, { 
        description: 'Get political party by ID',
        nullable: true 
    })
    async getPoliticalPartyById(
        @Args('id', { description: 'Political party ID' }) id: string
    ): Promise<PoliticalParty> {
        try {
            return await this.politicalPartyService.findById(id);
        } catch (error) {
            throw new Error(`Failed to fetch political party: ${error.message}`);
        }
    }

    @Mutation(() => PoliticalParty, { 
        description: 'Create a new political party' 
    })
    async createPoliticalParty(
        @Args('politicalParty', { description: 'Political party data' }) 
        politicalParty: InsertPoliticalPartyInput
    ): Promise<PoliticalParty> {
        try {
            return await this.politicalPartyService.create(politicalParty);
        } catch (error) {
            throw new Error(`Failed to create political party: ${error.message}`);
        }
    }

    @Mutation(() => PoliticalParty, { 
        description: 'Update an existing political party' 
    })
    async updatePoliticalParty(
        @Args('id', { description: 'Political party ID' }) id: string,
        @Args('politicalParty', { description: 'Updated political party data' }) 
        politicalParty: UpdatePoliticalPartyInput
    ): Promise<PoliticalParty> {
        try {
            return await this.politicalPartyService.update(id, politicalParty);
        } catch (error) {
            throw new Error(`Failed to update political party: ${error.message}`);
        }
    }

    @Mutation(() => Boolean, { 
        description: 'Delete a political party' 
    })
    async deletePoliticalParty(
        @Args('id', { description: 'Political party ID' }) id: string
    ): Promise<boolean> {
        try {
            return await this.politicalPartyService.delete(id);
        } catch (error) {
            throw new Error(`Failed to delete political party: ${error.message}`);
        }
    }
}
