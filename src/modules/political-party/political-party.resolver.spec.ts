import { Test, TestingModule } from '@nestjs/testing';
import { PoliticalPartyResolver } from './political-party.resolver';

describe('PoliticalPartyResolver', () => {
  let resolver: PoliticalPartyResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PoliticalPartyResolver],
    }).compile();

    resolver = module.get<PoliticalPartyResolver>(PoliticalPartyResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
