import { Field, InputType } from "@nestjs/graphql";
import { IsNotEmpty, IsString, MaxLength, MinLength } from "class-validator";

@InputType()
export class InsertPoliticalPartyInput {
    @Field(() => String)
    @IsNotEmpty({ message: 'Name is required' })
    @IsString({ message: 'Name must be a string' })
    @MinLength(2, { message: 'Name must be at least 2 characters long' })
    @MaxLength(100, { message: 'Name cannot exceed 100 characters' })
    name: string;

    @Field(() => String)
    @IsNotEmpty({ message: 'Short name is required' })
    @IsString({ message: 'Short name must be a string' })
    @MinLength(1, { message: 'Short name must be at least 1 character long' })
    @MaxLength(100, { message: 'Short name cannot exceed 100 characters' })
    short_name: string;
}