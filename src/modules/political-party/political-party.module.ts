import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PoliticalPartyResolver } from './political-party.resolver';
import { PoliticalPartyService } from './political-party.service';
import { PoliticalParty } from './entities/political-party.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PoliticalParty])],
  providers: [PoliticalPartyResolver, PoliticalPartyService]
})
export class PoliticalPartyModule {}
