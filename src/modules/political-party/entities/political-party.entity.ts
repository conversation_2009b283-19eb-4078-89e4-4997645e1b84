import { Field, ObjectType } from '@nestjs/graphql';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import { Candidate } from 'src/modules/candidate/entities/candidate.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

@ObjectType()
@Entity({ name: 'political_parties' })
export class PoliticalParty extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String)
  id: string;

  @Column({ length: 100 })
  @Field(() => String)
  name: string;

  @Column({ length: 100 })
  @Field(() => String)
  short_name: string;

  @Field(() => [Candidate], { nullable: true })
  @OneToMany(() => Candidate, (candidate) => candidate.political_party_id)
  candidates?: Candidate[];
}
