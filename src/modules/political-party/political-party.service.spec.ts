import { Test, TestingModule } from '@nestjs/testing';
import { PoliticalPartyService } from './political-party.service';

describe('PoliticalPartyService', () => {
  let service: PoliticalPartyService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PoliticalPartyService],
    }).compile();

    service = module.get<PoliticalPartyService>(PoliticalPartyService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
