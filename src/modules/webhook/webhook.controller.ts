import { Controller, Post, Body, HttpCode, Req } from '@nestjs/common';
import { WebhookService } from './webhook.service';
import { Request } from 'express';

@Controller('webhook')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @Post('catch-all')
  @HttpCode(200)
  async catchAll(@Body() body: any) {
    console.log('Received webhook payload:', body);
    await this.webhookService.savePayload(body);
    return { status: 'ok' };
  }

  @Post('catch-all2')
  @HttpCode(200)
  async catchAll2(@Req() req: Request) {
    const rawBody = req.body; // Body crudo (necesita middleware)
    console.log('req:',req);
    console.log('Received raw body:', rawBody);
    try {
      // Intenta parsear solo si es un string (para cuerpos malformados)
      const payload = typeof rawBody === 'string' ? JSON.parse(rawBody) : rawBody;
      console.log('Payload válido:', payload);
    } catch (error) {
      console.error('Error al parsear JSON:', rawBody, error.message);
    }

    return { status: 'ok' };
  }
} 