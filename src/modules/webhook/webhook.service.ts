import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebhookLog } from './entities/webhook-log.entity';

@Injectable()
export class WebhookService {
  constructor(
    @InjectRepository(WebhookLog)
    private readonly webhookLogRepository: Repository<WebhookLog>,
  ) {}

  async savePayload(payload: any) {
    const log = this.webhookLogRepository.create({ payload });
    return this.webhookLogRepository.save(log);
  }
} 