import { Test, TestingModule } from '@nestjs/testing';
import { WebhookService } from './webhook.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WebhookLog } from './entities/webhook-log.entity';
import { Repository } from 'typeorm';

// Test unitario para WebhookService, enfocado en el método savePayload

describe('WebhookService', () => {
  let service: WebhookService; // Instancia del servicio a testear
  let repo: Repository<WebhookLog>; // Mock del repositorio de WebhookLog

  // Antes de cada test, configura el módulo de testing y los mocks necesarios
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookService,
        // Mock del repositorio de WebhookLog usando getRepositoryToken
        {
          provide: getRepositoryToken(WebhookLog),
          useValue: {
            // Mock de la función create: retorna el payload recibido
            create: jest.fn().mockImplementation((payload) => payload),
            // Mock de la función save: simula guardar y retorna un objeto con id, payload y createdAt
            save: jest.fn().mockImplementation((log) => Promise.resolve({ id: 1, ...log, createdAt: new Date() })),
          },
        },
      ],
    }).compile();

    // Obtiene las instancias del servicio y el repositorio mockeado
    service = module.get<WebhookService>(WebhookService);
    repo = module.get<Repository<WebhookLog>>(getRepositoryToken(WebhookLog));
  });

  // Test básico: verifica que el servicio esté definido
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Test principal: verifica que savePayload guarde correctamente el payload
  it('should save payload', async () => {
    const payload = { test: 'data' }; // Payload de prueba
    const result = await service.savePayload(payload); // Llama al método a testear
    // Verifica que el método create fue llamado con el payload correcto
    expect(repo.create).toHaveBeenCalledWith({ payload });
    // Verifica que el método save fue llamado
    expect(repo.save).toHaveBeenCalled();
    // Verifica que el resultado contiene las propiedades esperadas
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('payload', payload);
    expect(result).toHaveProperty('createdAt');
  });
}); 