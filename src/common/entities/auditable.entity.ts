import { Field } from '@nestjs/graphql';
import { CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';

export abstract class AuditableEntity {
  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  @Field(() => Date)
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  @Field(() => Date)
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  @Field(() => Date, { nullable: true })
  deleted_at?: Date;
} 