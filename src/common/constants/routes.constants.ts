export const ROUTES = {
    INVITE: {
        BASE_PATH: '/wati/invite',
        // VALIDATE: '/validate'
    },
    MY_NETWORK: {
        BASE_PATH: '/my-network',
        // VALIDATE: '/validate'
    },
    // AUTH: {
    //     BASE_PATH: '/auth',
    //     LOGIN: '/login',
    //     REGISTER: '/register'
    // },
    // USER: {
    //     BASE_PATH: '/user',
    //     PROFILE: '/profile'
    // }
} as const;

export const generateInviteUrl = (baseUrl: string, inviteCode: string): string => {
    return `${baseUrl}${ROUTES.INVITE.BASE_PATH}/${inviteCode}`;
};
export const generateMyNetworkUrl = (baseUrl: string, accessCode: string): string => {
    return `${baseUrl}${ROUTES.MY_NETWORK.BASE_PATH}/${accessCode}`;
};

