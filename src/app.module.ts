import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AppResolver } from './app.resolver';
import { UserModule } from './modules/user/user.module';
import { WebhookModule } from './modules/webhook/webhook.module';
import { WatiModule } from './integrations/wati/wati.module';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriverConfig } from '@nestjs/apollo';
import { graphqlConfig } from './config/graphql.config';
import { PoliticalPartyModule } from './modules/political-party/political-party.module';
import { CandidateModule } from './modules/candidate/candidate.module';
import { CampaignModule } from './modules/campaign/campaign.module';
import { CampaignRegistrationsModule } from './modules/campaign-registrations/campaign-registrations.module';
import { CredentialsModule } from './modules/credentials/credentials.module';
import { RolesModule } from './modules/roles-permissions-management/roles/roles.module';
import { UsersRolesModule } from './modules/roles-permissions-management/users_roles/users_roles.module';
import { PermissionsModule } from './modules/roles-permissions-management/permissions/permissions.module';
import { RolesPermissionsModule } from './modules/roles-permissions-management/roles_permissions/roles_permissions.module';
import { PermissionOverridesModule } from './modules/roles-permissions-management/permission_overrides/permission_overrides.module';
import { PermissionDependenciesModule } from './modules/roles-permissions-management/permission_dependencies/permission_dependencies.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from './modules/auth/guards/permissions.guard';
import { AuthModule } from './modules/auth/auth.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: +(process.env.DB_PORT ?? 5432),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      autoLoadEntities: true,
      synchronize: true, // Solo para desarrollo, no usar en producción
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>(graphqlConfig),
    UserModule,
    WebhookModule,
    WatiModule,
    PoliticalPartyModule,
    CandidateModule,
    CampaignModule,
    CampaignRegistrationsModule,
    AuthModule,
    CredentialsModule,
    RolesModule,
    UsersRolesModule,
    PermissionsModule,
    RolesPermissionsModule,
    PermissionOverridesModule,
    PermissionDependenciesModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    AppResolver,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
  ],
})
export class AppModule {}
