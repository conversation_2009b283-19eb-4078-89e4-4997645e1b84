# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Credential {
  id: String!
  user_id: String!
}

type PermissionOverride {
  id: String!
  user_role_id: String!
  permission_id: String!
  action: String!
  permission: Permission!
  user_role: UserRole!
}

type PermissionDependency {
  permission_id: String!
  required_permission_id: String!
  permission: Permission!
  required_permission: Permission!
}

type Permission {
  id: String!
  name: String!
  module: String!
  code: String!
  description: String
  role_permissions: [RolePermission!]!
  permission_overrides: [PermissionOverride!]!
  dependencies: [PermissionDependency!]!
  required_for: [PermissionDependency!]!
}

type RolePermission {
  id: String!
  role_id: String!
  permission_id: String!
  role: Role!
  permission: Permission!
}

type Role {
  id: String!
  name: String!
  description: String
  users_roles: [UserRole!]!
  role_permissions: [RolePermission!]!
}

type UserRole {
  id: String!
  user_id: String!
  role_id: String!
  personalized: Boolean
  user: User!
  role: Role!
  permission_overrides: [PermissionOverride!]!
}

type User {
  id: String!
  email: String
  username: String

  """Nombre completo del usuario"""
  full_name: String
  phone: String!
  profile_picture: String
  campaign_registrations: [CampaignRegistration!]
  credential: Credential
  users_roles: [UserRole!]
}

type CampaignRegistration {
  id: String!
  campaign_id: String
  campaign: Campaign
  user_id: String
  user: User
  name: String!
  invite_code: String!
  invite_link: String
  referred_by_user_id: String
  registration_form: String
  session_access_code: String
  session_access_code_expires_at: Timestamp
}

"""
`Date` type as integer. Type represents date and time as number of milliseconds from start of UNIX epoch.
"""
scalar Timestamp

type Campaign {
  id: String!
  name: String!
  whatsapp_number: String
  wati_tenant_id: String
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  campaign_registrations: [CampaignRegistration!]
  database_host: String
  database_port: String
  database_username: String
  database_password: String
  database_name: String
}

type CampaignPaginatedResponse {
  """Lista de campañas para la página actual"""
  data: [Campaign!]!

  """Número total de campañas que coinciden con la búsqueda"""
  total: Int!
}

type Candidate {
  id: String!
  full_name: String!
  political_party_id: String
  political_party: PoliticalParty
}

type PoliticalParty {
  id: String!
  name: String!
  short_name: String!
  candidates: [Candidate!]
}

type Query {
  syncCampaignDatabase(campaignId: String!): String!
  findPaginatedCampaigns(search: String, paginationInput: PaginationInput): CampaignPaginatedResponse!
  getCampaignData(campaignId: String!): Boolean!

  """Get all political parties"""
  getPoliticalParties: [PoliticalParty!]

  """Get political party by ID"""
  getPoliticalPartyById(
    """Political party ID"""
    id: String!
  ): PoliticalParty
  getCandidates: [Candidate!]!
  candidate(id: String!): Candidate!
  generateHash(password: String!): String!
}

input PaginationInput {
  """Número de página"""
  page: Int! = 1

  """Resultados por página"""
  limit: Int! = 10
}

type Mutation {
  createCampaign(createCampaignInput: CreateCampaignInput!): Campaign!
  updateCampaign(updateCampaignInput: UpdateCampaignInput!): Campaign!

  """Create a new political party"""
  createPoliticalParty(
    """Political party data"""
    politicalParty: InsertPoliticalPartyInput!
  ): PoliticalParty!

  """Update an existing political party"""
  updatePoliticalParty(
    """Political party ID"""
    id: String!

    """Updated political party data"""
    politicalParty: UpdatePoliticalPartyInput!
  ): PoliticalParty!

  """Delete a political party"""
  deletePoliticalParty(
    """Political party ID"""
    id: String!
  ): Boolean!
  createCandidate(createCandidateInput: CreateCandidateInput!): Candidate!
  updateCandidate(updateCandidateInput: UpdateCandidateInput!): Candidate!
  deleteCandidate(id: String!): Candidate!
}

input CreateCampaignInput {
  name: String!
  whatsapp_number: String!
  wati_tenant_id: String!
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  token: String!
}

input UpdateCampaignInput {
  id: String!
  name: String!
  candidate_id: String!
  whatsapp_number: String!
  wati_tenant_id: String!
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  token: String
}

input InsertPoliticalPartyInput {
  name: String!
  short_name: String!
}

input UpdatePoliticalPartyInput {
  name: String
  short_name: String
}

input CreateCandidateInput {
  full_name: String!
  political_party_id: String
}

input UpdateCandidateInput {
  full_name: String
  political_party_id: String
  id: String!
}