# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type PoliticalParty {
  id: String!
  name: String!
  short_name: String!
  candidates: [Candidate!]
}

type Candidate {
  id: String!
  full_name: String!
  political_party_id: String
  political_party: PoliticalParty
  campaigns: [Campaign!]
}

type Campaign {
  id: String!
  name: String!
  candidate: Candidate
  whatsapp_number: String
  wati_tenant_id: String
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  campaign_registrations: [CampaignRegistration!]
}

"""
`Date` type as integer. Type represents date and time as number of milliseconds from start of UNIX epoch.
"""
scalar Timestamp

type CampaignRegistration {
  id: String!
  campaign_id: String
  campaign: Campaign
  user_id: String
  user: User
  name: String!
  invite_code: String!
  invite_link: String
  referred_by_user_id: String
  registration_form: String
}

type User {
  id: String!
  phone: String!
  campaign_registrations: [CampaignRegistration!]
}

type Query {
  """Get all political parties"""
  getPoliticalParties: [PoliticalParty!]

  """Get political party by ID"""
  getPoliticalPartyById(
    """Political party ID"""
    id: String!
  ): PoliticalParty
  getCandidates: [Candidate!]!
  candidate(id: String!): Candidate!
}

type Mutation {
  createCampaign(createCampaignInput: CreateCampaignInput!): Campaign!
  updateCampaign(updateCampaignInput: UpdateCampaignInput!): Campaign!

  """Create a new political party"""
  createPoliticalParty(
    """Political party data"""
    politicalParty: InsertPoliticalPartyInput!
  ): PoliticalParty!

  """Update an existing political party"""
  updatePoliticalParty(
    """Political party ID"""
    id: String!

    """Updated political party data"""
    politicalParty: UpdatePoliticalPartyInput!
  ): PoliticalParty!

  """Delete a political party"""
  deletePoliticalParty(
    """Political party ID"""
    id: String!
  ): Boolean!
  createCandidate(createCandidateInput: CreateCandidateInput!): Candidate!
  updateCandidate(updateCandidateInput: UpdateCandidateInput!): Candidate!
  deleteCandidate(id: String!): Candidate!
}

input CreateCampaignInput {
  name: String!
  candidate_id: String!
  whatsapp_number: String!
  wati_tenant_id: String!
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  token: String!
}

input UpdateCampaignInput {
  id: String!
  name: String!
  candidate_id: String!
  whatsapp_number: String!
  wati_tenant_id: String!
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  token: String
}

input InsertPoliticalPartyInput {
  name: String!
  short_name: String!
}

input UpdatePoliticalPartyInput {
  name: String
  short_name: String
}

input CreateCandidateInput {
  full_name: String!
  political_party_id: String
}

input UpdateCandidateInput {
  full_name: String
  political_party_id: String
  id: String!
}