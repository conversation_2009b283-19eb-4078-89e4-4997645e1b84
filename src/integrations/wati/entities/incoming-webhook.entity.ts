import { Field } from '@nestjs/graphql';
import { AuditableEntity } from 'src/common/entities/auditable.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('incoming_webhooks')
export class IncomingWebhook extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Field(() => String, { nullable: true })
  tenantId: string;

  @Column({ type: 'varchar', length: 100 })
  @Field(() => String)
  provider: string; // 'WATI'

  @Column({ type: 'varchar', length: 100 })
  @Field(() => String)
  eventType: string; // 'newContactMessageReceived', 'messageTemplateStatusUpdate', etc.

  @Column({ type: 'jsonb' })
  @Field(() => JSON)
  rawPayload: any; // Almacena el payload completo en formato JSON

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Field(() => String)
  waId: string; // Identificador de WhatsApp

//   @Column({ type: 'boolean', default: false })
//   processed: boolean; // Indica si el webhook ha sido procesado

//   @Column({ type: 'text', nullable: true })
//   processingError: string; // Mensaje de error si falla el procesamiento
}