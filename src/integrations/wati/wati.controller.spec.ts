import { Test, TestingModule } from '@nestjs/testing';
import { WatiController } from './wati.controller';

describe('WatiController', () => {
  let controller: WatiController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WatiController],
    }).compile();

    controller = module.get<WatiController>(WatiController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
