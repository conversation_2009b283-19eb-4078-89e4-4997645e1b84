import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { NewContactMessageReceivedEvent } from './interfaces/new-contact-message-received.interface';
import { IncomingWebhook } from './entities/incoming-webhook.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserService } from 'src/modules/user/user.service';
import { User } from 'src/modules/user/entities/user.entity';
import { AxiosResponse } from 'axios';
import { DataWebhookRegistrationForm } from './wati.controller';
import { Campaign } from 'src/modules/campaign/entities/campaign.entity';
import { CampaignService } from 'src/modules/campaign/campaign.service';
import { CampaignRegistration } from 'src/modules/campaign-registrations/entities/campaign-registration.entity';
import { CampaignRegistrationsService } from 'src/modules/campaign-registrations/campaign-registrations.service';
import { EncryptionService } from 'src/encryption/encryption.service';

export enum EventType {
  NEW_CONTACT_MESSAGE_RECEIVED = 'newContactMessageReceived', // El valor puede permanecer en camelCase si es requerido por la API externa
  REGISTRATION_FORM = 'registrationForm'
}

export interface WebhookNewContactMessagePayload {
  "eventType": string,
  "id": string,
  "created": string,
  "waId": string,
  "senderName": string,
  "sourceId": string | null,
  "sourceUrl": string | null,
  "sourceType": number
}

@Injectable()
export class WatiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(IncomingWebhook)
    private incoming_webhookRepository: Repository<IncomingWebhook>,
    private readonly userService: UserService,
    private readonly campaignsService: CampaignService,
    private readonly campaignRegistrationsService: CampaignRegistrationsService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async handleWebhookNewContactMessage(tenantId:string, payload: WebhookNewContactMessagePayload):Promise<void>{
    const campaign:Campaign | null = await this.campaignsService.getCampaignByTenantId(tenantId);
    if (!campaign) {
      throw new Error('Campaign not found');
    }
    const i_w = new IncomingWebhook();
    i_w.provider = 'WATI';
    i_w.tenantId = tenantId;
    i_w.eventType = EventType.NEW_CONTACT_MESSAGE_RECEIVED;
    i_w.rawPayload = payload;
    i_w.waId = payload.waId;

    const i_wEntity: IncomingWebhook =
      this.incoming_webhookRepository.create(i_w);
    await this.incoming_webhookRepository.save(i_wEntity);

    const user:User = await this.userService.saveUser(payload);
    let campaignRegistration:CampaignRegistration | null = await this.campaignRegistrationsService.getCampaignRegistrationByCampaignIdAndUserId(campaign.id, user.id);
    if (!campaignRegistration){
      campaignRegistration= await this.campaignRegistrationsService.insertCampaignRegistration(campaign.id, user, payload);
    }
    console.log(`"user: ${user}`);
    
    // const dev_phone="59167653557"
    await this.updateContactAttributes(tenantId, i_w.waId, [
      {
        name: "invite_code",
        value: campaignRegistration.invite_code,
      },
      {
        name: "invite_link",
        value: campaignRegistration.invite_link,
      },
      {
        name: "contact_data",
        value: JSON.stringify({
          tenantId: tenantId,
          phone: user.phone,
        })
      }
    ]);
  }

  async updateContactAttributes(
    tenantId: string,
    contactId: string,
    customParams: { name: string; value: any }[],
  ) {
    try {
    const baseUrl = this.configService.get<string>('WATI_BASE_URL')+tenantId;
    const campaign = await this.campaignsService.getCampaignByTenantId(tenantId);
    if (!campaign) {
      throw new Error('Campaign not found');
    }
    if (!campaign.wati_token) {
      throw new Error('Campaign token not found');  
    }
    const token = this.encryptionService.decrypt(campaign.wati_token);
    console.log('token',token);
    const url = `${baseUrl}/api/v1/updateContactAttributes/${contactId}`;
    console.log('url',url);
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    console.log('customParams',customParams);

    const body = {
      customParams: customParams
    };

    try {
      const response: AxiosResponse = await firstValueFrom(
        this.httpService.post(url, body, { headers })
      );
      
      console.log('Wati API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating contact attributes:', error.response?.data || error.message);
      throw new Error('Failed to update contact attributes');
    }

  } catch (error) {
      console.error('Error updating contact attributes:', error.message);
      throw new Error('Failed to update contact attributes');
    }
  }

 

  async handleWebhookRegistrationForm(tenantId:string, payload: DataWebhookRegistrationForm): Promise<void> {
    const payloadJson = payload;
    console.log('payloadJson:', payloadJson);
    const i_w = new IncomingWebhook();
    i_w.provider = 'WATI';
    i_w.tenantId = tenantId;
    i_w.eventType = EventType.REGISTRATION_FORM;
    i_w.rawPayload = payloadJson;
    i_w.waId = payloadJson.phone;

    const i_wEntity: IncomingWebhook =
      this.incoming_webhookRepository.create(i_w);
    const guardado=await this.incoming_webhookRepository.save(i_wEntity);
    console.log('Guardado de registro:', guardado);
    let user = await this.userService.findUser_ByPhone(payloadJson.phone);
    if(!user){
      user = await this.userService.createUser(payloadJson.phone);
    }
    await this.campaignRegistrationsService.updateRegistrationForm(tenantId, user.phone, payloadJson.registro_content);
  }
}
