import { Modu<PERSON> } from '@nestjs/common';
import { WatiService } from './wati.service';
import { HttpModule } from '@nestjs/axios';
import { WatiController } from './wati.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IncomingWebhook } from './entities/incoming-webhook.entity';
import { UserModule } from 'src/modules/user/user.module';
import { EncryptionModule } from 'src/encryption/encryption.module';
import { CampaignModule } from 'src/modules/campaign/campaign.module';
import { CampaignRegistrationsModule } from 'src/modules/campaign-registrations/campaign-registrations.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forFeature([IncomingWebhook]),
    HttpModule,
    UserModule,
    EncryptionModule,
    CampaignModule,
    CampaignRegistrationsModule,
    ConfigModule,
  ],
  providers: [WatiService],
  exports: [WatiService],
  controllers: [WatiController],
})
export class WatiModule {}
