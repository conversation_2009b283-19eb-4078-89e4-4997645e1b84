import {
  Controller,
  Post,
  Body,
  HttpCode,
  Get,
  Param,
  Res,
  Req,
} from '@nestjs/common';
import { WatiService } from './wati.service';
import { NewContactMessageReceivedEvent } from './interfaces/new-contact-message-received.interface';
import { Response } from 'express';
import { UserService } from 'src/modules/user/user.service';
import { CampaignRegistrationsService } from 'src/modules/campaign-registrations/campaign-registrations.service';
import { DataWebhookRegistrationForm } from './interfaces/webhook.interface';

export interface WatiAtributeContactData{
  tenantId: string,
  phone: string,
}

@Controller('wati')
export class WatiController {
  constructor(
    private readonly watiService: WatiService,
    private readonly userService: UserService,
    private readonly campaignRegistrationsService: CampaignRegistrationsService,
  ) {}

@Post('webhook/new-contact/:tenantId')
  async handleNewContactMessage(
    @Param('tenantId') tenantId: string,
    @Body() payload: any,
  ) {
    console.log('Received new contact message payload:', payload);
    let payloadJson: NewContactMessageReceivedEvent = payload;
    console.log('Parsed payload:', payloadJson);
    // payloadJson.waId = '59167653557';
    await this.watiService.handleWebhookNewContactMessage(tenantId ,payloadJson);
    return { status: 'ok' };
  }

  @Post('webhook/registration-form/:tenantId')
  @HttpCode(200)
  async handleRegistrationForm(
    @Param('tenantId') tenantId: string,
    @Body() payload: any
  ) {
    console.log('tenantId', tenantId);
    console.log('Received registration form payload:', payload);
    let data: DataWebhookRegistrationForm = {
      ...payload,
      registro_content: JSON.parse(payload.registro_content),
    };
    this.watiService.handleWebhookRegistrationForm(tenantId, data);
    return { status: 'ok' };
  }

  @Post('webhook/generateMyNetworkAccessLink')
  @HttpCode(200)
  async generateMyNetworkAccessLink(@Body() payload:any) {
    console.log('Received generate my network access link payload:', payload);
    const conntactData:WatiAtributeContactData= JSON.parse(payload.contactData);
    const myNetworkAccessLink= await this.campaignRegistrationsService.generateMyNetworkAccessLink(conntactData);
    return { 
      status: 'ok',
      my_network_access_link: myNetworkAccessLink
    };
  }

  @Post('webhook/simulationUpdateContactAttributes')
  @HttpCode(200)
  async simulationUpdateContactAttributes(@Body() payload: any) {
    console.log(
      'Received simulation update contact attributes payload:',
      payload,
    );
    const contactId = '59167653557';
    const customParams = [
      {
        name: 'codigo_invitacion_user',
        value: '123456',
      },
    ];
    // Nota: simulación usa un tenantId de prueba. Cambia según tu entorno.
    const tenantId = 'dev-tenant';
    await this.watiService.updateContactAttributes(tenantId, contactId, customParams);
    return { status: 'ok' };
  }

  // @Get('invite/:inviteCode')
  // async get0InviteLink(
  //   @Param('inviteCode') inviteCode: string,
  //   @Res() res: Response,
  // ) {
  //   console.log('Received invite code:', inviteCode);
  //   let user:any = await this.userService.findUser_ByInviteCode(inviteCode);
  //   const data: {
  //     invite_name: string;
  //     campaing_whatsapp_number: string;
  //   } = {
  //     invite_name: user.name || 'Cesar',
  //     campaing_whatsapp_number: '59164432507',
  //   };
  //   const mensaje = `Hola, soy ${data.invite_name} y te invito a conectarte con Edwin Martín. Haz clic en este enlace para completar la conexión 👇 https://brain.fundacionnapp.org/wati/open-chat/${data.campaing_whatsapp_number}`;
  //   const mensajeCodificado = encodeURIComponent(mensaje);
  //   const linkFinal = `https://wa.me/?text=${mensajeCodificado}`;
  //   console.log('Generated invite link:', linkFinal);
  //   return res.redirect(linkFinal);
  // }

  @Get('open-chat/:whatsappNumber')
  openChat(
    @Param('whatsappNumber') whatsappNumber: string,
    @Res() res: Response,
  ) {
    const nombreCandidato = 'el candidato Edwin Martín'; // fallback
    const mensaje = `Hola, quiero conectarme con ${nombreCandidato}.`;
    const mensajeCodificado = encodeURIComponent(mensaje);
    const linkFinal = `https://wa.me/${whatsappNumber}?text=${mensajeCodificado}`;
    return res.redirect(linkFinal);
  }
}
