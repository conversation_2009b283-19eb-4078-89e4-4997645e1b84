import { Args, Query, Resolver } from '@nestjs/graphql';
import { DatabaseManagerService } from './database-manager.service';
import { Public } from 'src/common/decorators/public.decorator';

@Resolver()
export class DatabaseManagerResolver {

  constructor(private readonly databaseManagerService: DatabaseManagerService) {}

  @Public()
  @Query(() => String)
  async syncCampaignDatabase(@Args('campaignId') campaignId: string): Promise<string> {
    await this.databaseManagerService.syncCampaignDatabase(campaignId);
    return 'Sincronización completada';
  }

}
