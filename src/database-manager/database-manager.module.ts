import { Global, Module } from '@nestjs/common';
import { DatabaseManagerService } from './database-manager.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseManagerResolver } from './database-manager.resolver';
import { Campaign } from 'src/modules/campaign/entities/campaign.entity';

@Global()
@Module({
  imports: [
    ConfigModule
  ],
  providers: [DatabaseManagerService, DatabaseManagerResolver],
  exports: [DatabaseManagerService],
})
export class DatabaseManagerModule {}
