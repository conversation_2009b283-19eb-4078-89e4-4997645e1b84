import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource } from '@nestjs/typeorm';
import { Campaign } from 'src/modules/campaign/entities/campaign.entity';
import { CampaignUser } from 'src/modules/campaign/modules/campaign-users/entities/campaign-user.entity';
import { DataSource, DataSourceOptions, Repository } from 'typeorm';

@Injectable()
export class DatabaseManagerService {
  private readonly logger = new Logger(DatabaseManagerService.name);
  private campaignConnections = new Map<string, DataSource>();

  constructor(
    private configService: ConfigService,
    @InjectDataSource()
    private mainDataSource: DataSource,
  ) {}

  /**
   * Obtiene la conexión a la base de datos de una campaña específica
   */
  async getCampaignConnection(campaignId: string): Promise<DataSource> {
    // Si ya existe la conexión, la retornamos
    if (this.campaignConnections.has(campaignId)) {
      return this.campaignConnections.get(campaignId)!;
    }

    console.log('__dirname', __dirname);

    // Buscamos la campaña en la DB central
    const campaignRepository = this.mainDataSource.getRepository(Campaign);
    const campaign = await campaignRepository.findOne({
      where: { id: campaignId },
    });

    if (!campaign) {
      throw new Error(`Campaign with ID ${campaignId} not found`);
    }

    //creamos la conexion a la DB de la campaña
    const campaignDbOptions: DataSourceOptions = {
      type: 'postgres',
      host: campaign.database_host || this.configService.get('DB_HOST'),
      port: campaign.database_port || this.configService.get('DB_PORT'),
      // username:
      //   campaign.database_username || this.configService.get('DB_USERNAME'),
      // password:
      //   campaign.database_password || this.configService.get('DB_PASSWORD'),
      username: this.configService.get('DB_USERNAME'),
      password: this.configService.get('DB_PASSWORD'),
      database: campaign.database_name,
      synchronize: false,
      logging: this.configService.get('NODE_ENV') === 'development',
      entities: [CampaignUser],
    };

    const campaignDataSource = new DataSource(campaignDbOptions);
    await campaignDataSource.initialize();

    // Guardamos la conexión en el Map
    this.campaignConnections.set(campaignId, campaignDataSource);

    this.logger.log(`Conexión establecida para campaña: ${campaign.name}`);
    return campaignDataSource;
  }

  /**
   * Cierra todas las conexiones de campañas
   */
  async closeAllConnections(): Promise<void> {
    for (const [campaignId, connection] of this.campaignConnections) {
      await connection.destroy();
      this.logger.log(`Conexión cerrada para campaña: ${campaignId}`);
    }
    this.campaignConnections.clear();
  }

  /**
   * Cierra la conexión de una campaña específica
   */
  async closeCampaignConnection(campaignId: string): Promise<void> {
    const connection = this.campaignConnections.get(campaignId);
    if (connection) {
      await connection.destroy();
      this.campaignConnections.delete(campaignId);
      this.logger.log(`Conexión cerrada para campaña: ${campaignId}`);
    }
  }

  /**
   * Sincroniza las tablas de una campaña específica (solo una vez)
   */
  async syncCampaignDatabase(campaignId: string): Promise<void> {
    try {
      this.logger.log(`Iniciando sincronización para campaña ${campaignId}`);

      // Obtener conexión
      const campaignConnection = await this.getCampaignConnection(campaignId);

      // Ejecutar sincronización manualmente
      await campaignConnection.synchronize();

      this.logger.log(`Sincronización completada para campaña ${campaignId}`);
    } catch (error) {
      this.logger.error(`Error sincronizando campaña ${campaignId}:`, error);
      throw error;
    }
  }
}
