# Diagrama de Flujo - Webhook New Contact

```mermaid
flowchart TD
    A["🌐 Webhook Request<br/>POST /wati/webhook/new-contact/:tenantId"] --> B["📥 WatiController.handleNewContactMessage"]
    
    B --> C["📝 Log payload"]
    C --> D["🔧 Parse payload to NewContactMessageReceivedEvent"]
    D --> E["📤 Call WatiService.handleWebhookNewContactMessage"]
    
    E --> F["🔍 Get Campaign by tenantId"]
    F --> G{"🏢 Campaign exists?"}
    
    G -->|No| H["❌ Throw Error: Campaign not found"]
    G -->|Yes| I["📦 Create IncomingWebhook entity"]
    
    I --> J["💾 Save webhook to DB<br/>Table: incoming_webhooks"]
    J --> K["👤 Call UserService.saveUser"]
    
    K --> L["🔍 Find user by phone/waId"]
    L --> M{"👤 User exists?"}
    
    M -->|No| N["➕ Create new User<br/>phone = waId"]
    M -->|Yes| O["🔍 Check if campaign registration exists"]
    
    N --> P["💾 Save new User to DB"]
    P --> Q["➕ Create CampaignRegistration"]
    
    O --> R{"📋 Registration exists?"}
    R -->|Yes| S["✅ Return existing user"]
    R -->|No| Q
    
    Q --> T["🎫 Generate invite_code uuid"]
    T --> U["🔗 Generate invite_link using routes.constants"]
    U --> V["💾 Save CampaignRegistration to DB"]
    
    V --> W["📞 Call updateContactAttributes on WATI API"]
    W --> X["🔐 Decrypt campaign token"]
    X --> Y["🌐 HTTP POST to WATI API<br/>with invite_code & invite_link"]
    
    Y --> Z["✅ Return success response<br/>{status: ok}"]
    
    style A fill:#e1f5fe
    style H fill:#ffebee
    style Z fill:#e8f5e8
    style W fill:#fff3e0
    style J fill:#f3e5f5
    style V fill:#f3e5f5
```

## Cómo visualizar este diagrama:

1. **En VS Code/Cursor**: Instala la extensión "Mermaid Preview" 
2. **En GitHub**: Se renderiza automáticamente
3. **Online**: Copia el código mermaid en https://mermaid.live/
4. **En documentación**: Muchas plataformas soportan mermaid nativamente

## Código Mermaid (para copiar):

```
flowchart TD
    A["🌐 Webhook Request<br/>POST /wati/webhook/new-contact/:tenantId"] --> B["📥 WatiController.handleNewContactMessage"]
    
    B --> C["📝 Log payload"]
    C --> D["🔧 Parse payload to NewContactMessageReceivedEvent"]
    D --> E["📤 Call WatiService.handleWebhookNewContactMessage"]
    
    E --> F["🔍 Get Campaign by tenantId"]
    F --> G{"🏢 Campaign exists?"}
    
    G -->|No| H["❌ Throw Error: Campaign not found"]
    G -->|Yes| I["📦 Create IncomingWebhook entity"]
    
    I --> J["💾 Save webhook to DB<br/>Table: incoming_webhooks"]
    J --> K["👤 Call UserService.saveUser"]
    
    K --> L["🔍 Find user by phone/waId"]
    L --> M{"👤 User exists?"}
    
    M -->|No| N["➕ Create new User<br/>phone = waId"]
    M -->|Yes| O["🔍 Check if campaign registration exists"]
    
    N --> P["💾 Save new User to DB"]
    P --> Q["➕ Create CampaignRegistration"]
    
    O --> R{"📋 Registration exists?"}
    R -->|Yes| S["✅ Return existing user"]
    R -->|No| Q
    
    Q --> T["🎫 Generate invite_code uuid"]
    T --> U["🔗 Generate invite_link using routes.constants"]
    U --> V["💾 Save CampaignRegistration to DB"]
    
    V --> W["📞 Call updateContactAttributes on WATI API"]
    W --> X["🔐 Decrypt campaign token"]
    X --> Y["🌐 HTTP POST to WATI API<br/>with invite_code & invite_link"]
    
    Y --> Z["✅ Return success response<br/>{status: ok}"]
    
    style A fill:#e1f5fe
    style H fill:#ffebee
    style Z fill:#e8f5e8
    style W fill:#fff3e0
    style J fill:#f3e5f5
    style V fill:#f3e5f5
```
