
Estoy desarrollando desde cero un sistema backend en NestJS con base de datos relacional (PostgreSQL o MySQL) y una futura interfaz web en Angular.
El objetivo es construir una plataforma de referidos multilevel basada en WhatsApp, pensada inicialmente para campañas políticas pero extensible a otros usos.

El flujo funciona así: cada persona entra al chatbot de WhatsApp (vía Wati), completa un formulario, y automáticamente se le asigna un código de invitación (invite_code) para invitar a otros mediante un link compartible. El backend guarda la red de referidos jerá<PERSON>ca, permite estadísticas por usuario (cantidad de invitados, niveles alcanzados), y genera enlaces personalizados como https://mi-dominio.com/numero.

El sistema debe tener capacidad para:

Manejar múltiples campañas (cada una con su propio número de WhatsApp)

Generar códigos únicos por persona

Detectar cuándo una persona entra con código de referido (#ABC123)

Registrar conversaciones, etiquetas, datos de contacto

Enviar mensajes personalizados según el perfil de cada persona

Escalar como CRM conversacional para entender necesidades de la población

La lógica del chatbot, los formularios, los códigos y los envíos se gestiona principalmente desde el backend, usando la API de Wati como canal. Mi objetivo es construir un MVP funcional en 3-4 semanas y luego escalarlo como SaaS personalizable.


🎯 PROMPT DEL SISTEMA MINOS
MINOS es una plataforma backend de referidos multinivel basada en WhatsApp, desarrollada en NestJS con PostgreSQL, diseñada inicialmente para campañas políticas pero extensible a otros sectores. El sistema integra chatbots de WhatsApp (vía WATI) con un backend robusto que gestiona redes de referidos jerárquicas, formularios dinámicos y análisis de conversaciones.

🚀 CARACTERÍSTICAS PRINCIPALES
🏗️ Arquitectura y Tecnologías
Backend: NestJS con TypeScript
Base de Datos: PostgreSQL con TypeORM
API: GraphQL (Apollo Server) + REST endpoints
Integración WhatsApp: WATI API
Seguridad: Encriptación AES-256-GCM para tokens sensibles
Acortador de URLs: Integración con TinyURL API
📱 Funcionalidades Core
1. Sistema de Referidos Multinivel
Generación automática de códigos únicos de invitación (UUID)
Enlaces personalizados para compartir (/wati/invite/{codigo})
Tracking de referidos jerárquicos con  referred_by_user_id
Red de referidos escalable y auditable
2. Gestión de Campañas Políticas
Múltiples campañas simultáneas con números de WhatsApp independientes
Candidatos asociados a partidos políticos
Fechas de campaña configurables (inicio/fin)
Tenant isolation por campaña usando wati_tenant_id
3. Integración WhatsApp (WATI)
Webhooks automáticos para nuevos contactos
Formularios dinámicos capturados vía WhatsApp
Sincronización bidireccional de atributos de contacto
Mensajes personalizados según perfil del usuario
4. Gestión de Usuarios
Identificación por teléfono (número de WhatsApp)
Registro automático al primer contacto
Historial de registros en múltiples campañas
Datos de formulario almacenados en formato JSONB
🔧 Módulos del Sistema
Módulos Principales
UserModule: Gestión de usuarios y perfiles
CampaignModule: Administración de campañas
CampaignRegistrationsModule: Registros y códigos de invitación
CandidateModule: Gestión de candidatos políticos
PoliticalPartyModule: Partidos políticos
WebhookModule: Captura general de webhooks
WatiModule: Integración específica con WATI
Servicios de Soporte
EncryptionService: Encriptación segura de tokens
TinyUrlService: Acortamiento de enlaces
WatiService: Comunicación con API de WATI
🗄️ Modelo de Datos
Entidades Principales
Users: Usuarios identificados por teléfono
Campaigns: Campañas con configuración WATI
CampaignRegistrations: Registros con códigos únicos
Candidates: Candidatos políticos
PoliticalParties: Partidos políticos
IncomingWebhooks: Auditoría de webhooks recibidos
Características de Datos
UUIDs como identificadores primarios
Auditoría automática (created_at, updated_at, deleted_at)
Campos únicos para códigos e invitaciones
JSONB para formularios flexibles
🔄 Flujos de Trabajo
Flujo de Nuevo Contacto
Usuario envía mensaje a WhatsApp de campaña
WATI envía webhook al backend
Sistema crea/actualiza usuario automáticamente
Genera código único de invitación
Sincroniza datos de vuelta a WATI
Usuario recibe su código para compartir
Flujo de Formulario de Registro
Usuario completa formulario en WhatsApp
Datos se almacenan en formato JSONB
Se actualiza registro de campaña
Información disponible para análisis
🛡️ Seguridad y Configuración
Tokens encriptados en base de datos
Variables de entorno para configuración sensible
Validación de tenants antes de procesar webhooks
Auditoría completa de todas las interacciones
📊 Capacidades de Escalamiento
Multi-tenant por diseño
GraphQL para consultas eficientes
Arquitectura modular para extensibilidad
Preparado para SaaS con configuración por campaña
🎯 Casos de Uso
Campañas políticas con redes de referidos
Marketing multinivel vía WhatsApp
CRM conversacional para análisis de población
Sistemas de invitación escalables
Este sistema está diseñado como un MVP funcional que puede escalarse como SaaS personalizable para diferentes sectores más allá de la política.