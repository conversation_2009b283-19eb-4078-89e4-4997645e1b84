
Estoy desarrollando desde cero un sistema backend en NestJS con base de datos relacional (PostgreSQL o MySQL) y una futura interfaz web en Angular.
El objetivo es construir una plataforma de referidos multilevel basada en WhatsApp, pensada inicialmente para campañas políticas pero extensible a otros usos.

El flujo funciona así: cada persona entra al chatbot de WhatsApp (vía Wati), completa un formulario, y automáticamente se le asigna un código de invitación (invite_code) para invitar a otros mediante un link compartible. El backend guarda la red de referidos jerá<PERSON>ca, permite estadísticas por usuario (cantidad de invitados, niveles alcanzados), y genera enlaces personalizados como https://mi-dominio.com/numero.

El sistema debe tener capacidad para:

Manejar múltiples campañas (cada una con su propio número de WhatsApp)

Generar códigos únicos por persona

Detectar cuándo una persona entra con código de referido (#ABC123)

Registrar conversaciones, etiquetas, datos de contacto

Enviar mensajes personalizados según el perfil de cada persona

Escalar como CRM conversacional para entender necesidades de la población

La lógica del chatbot, los formularios, los códigos y los envíos se gestiona principalmente desde el backend, usando la API de Wati como canal. Mi objetivo es construir un MVP funcional en 3-4 semanas y luego escalarlo como SaaS personalizable.


