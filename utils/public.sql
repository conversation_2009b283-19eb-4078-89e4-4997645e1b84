/*
 Navicat Premium Dump SQL

 Source Server         : local_docker_postgres
 Source Server Type    : PostgreSQL
 Source Server Version : 150013 (150013)
 Source Host           : localhost:5432
 Source Catalog        : 
countries
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 150013 (150013)
 File Encoding         : 65001

 Date: 02/09/2025 17:28:48
*/


-- ----------------------------
-- Sequence structure for cities_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."cities_id_seq";
CREATE SEQUENCE "public"."cities_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."cities_id_seq" OWNER TO "postgres";

-- ----------------------------
-- Sequence structure for countries_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."countries_id_seq";
CREATE SEQUENCE "public"."countries_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."countries_id_seq" OWNER TO "postgres";

-- ----------------------------
-- Sequence structure for regions_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."regions_id_seq";
CREATE SEQUENCE "public"."regions_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."regions_id_seq" OWNER TO "postgres";

-- ----------------------------
-- Sequence structure for states_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."states_id_seq";
CREATE SEQUENCE "public"."states_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."states_id_seq" OWNER TO "postgres";

-- ----------------------------
-- Sequence structure for subregions_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."subregions_id_seq";
CREATE SEQUENCE "public"."subregions_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."subregions_id_seq" OWNER TO "postgres";

-- ----------------------------
-- Table structure for cities
-- ----------------------------
DROP TABLE IF EXISTS "public"."cities";
CREATE TABLE "public"."cities" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "state_id" int8 NOT NULL,
  "state_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "country_id" int8 NOT NULL,
  "country_code" char(2) COLLATE "pg_catalog"."default" NOT NULL,
  "latitude" numeric(10,8) NOT NULL,
  "longitude" numeric(11,8) NOT NULL,
  "timezone" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) NOT NULL DEFAULT '2014-01-01 12:01:01'::timestamp without time zone,
  "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "flag" int2 NOT NULL DEFAULT 1,
  "wikiDataId" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."cities" OWNER TO "postgres";
COMMENT ON COLUMN "public"."cities"."timezone" IS 'IANA timezone identifier (e.g., America/New_York)';
COMMENT ON COLUMN "public"."cities"."wikiDataId" IS 'Rapid API GeoDB Cities';

-- ----------------------------
-- Table structure for countries
-- ----------------------------
DROP TABLE IF EXISTS "public"."countries";
CREATE TABLE "public"."countries" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "iso3" char(3) COLLATE "pg_catalog"."default",
  "numeric_code" char(3) COLLATE "pg_catalog"."default",
  "iso2" char(2) COLLATE "pg_catalog"."default",
  "phonecode" varchar(255) COLLATE "pg_catalog"."default",
  "capital" varchar(255) COLLATE "pg_catalog"."default",
  "currency" varchar(255) COLLATE "pg_catalog"."default",
  "currency_name" varchar(255) COLLATE "pg_catalog"."default",
  "currency_symbol" varchar(255) COLLATE "pg_catalog"."default",
  "tld" varchar(255) COLLATE "pg_catalog"."default",
  "native" varchar(255) COLLATE "pg_catalog"."default",
  "region" varchar(255) COLLATE "pg_catalog"."default",
  "region_id" int8,
  "subregion" varchar(255) COLLATE "pg_catalog"."default",
  "subregion_id" int8,
  "nationality" varchar(255) COLLATE "pg_catalog"."default",
  "timezones" text COLLATE "pg_catalog"."default",
  "translations" text COLLATE "pg_catalog"."default",
  "latitude" numeric(10,8),
  "longitude" numeric(11,8),
  "emoji" varchar(191) COLLATE "pg_catalog"."default",
  "emojiU" varchar(191) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "flag" int2 NOT NULL DEFAULT 1,
  "wikiDataId" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."countries" OWNER TO "postgres";
COMMENT ON COLUMN "public"."countries"."wikiDataId" IS 'Rapid API GeoDB Cities';

-- ----------------------------
-- Table structure for regions
-- ----------------------------
DROP TABLE IF EXISTS "public"."regions";
CREATE TABLE "public"."regions" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "translations" text COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "flag" int2 NOT NULL DEFAULT 1,
  "wikiDataId" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."regions" OWNER TO "postgres";
COMMENT ON COLUMN "public"."regions"."wikiDataId" IS 'Rapid API GeoDB Cities';

-- ----------------------------
-- Table structure for states
-- ----------------------------
DROP TABLE IF EXISTS "public"."states";
CREATE TABLE "public"."states" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "country_id" int8 NOT NULL,
  "country_code" char(2) COLLATE "pg_catalog"."default" NOT NULL,
  "fips_code" varchar(255) COLLATE "pg_catalog"."default",
  "iso2" varchar(255) COLLATE "pg_catalog"."default",
  "iso3166_2" varchar(10) COLLATE "pg_catalog"."default",
  "type" varchar(191) COLLATE "pg_catalog"."default",
  "level" int4,
  "parent_id" int8,
  "native" varchar(255) COLLATE "pg_catalog"."default",
  "latitude" numeric(10,8),
  "longitude" numeric(11,8),
  "timezone" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "flag" int2 NOT NULL DEFAULT 1,
  "wikiDataId" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."states" OWNER TO "postgres";
COMMENT ON COLUMN "public"."states"."timezone" IS 'IANA timezone identifier (e.g., America/New_York)';
COMMENT ON COLUMN "public"."states"."wikiDataId" IS 'Rapid API GeoDB Cities';

-- ----------------------------
-- Table structure for subregions
-- ----------------------------
DROP TABLE IF EXISTS "public"."subregions";
CREATE TABLE "public"."subregions" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1
),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "translations" text COLLATE "pg_catalog"."default",
  "region_id" int8 NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "flag" int2 NOT NULL DEFAULT 1,
  "wikiDataId" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."subregions" OWNER TO "postgres";
COMMENT ON COLUMN "public"."subregions"."wikiDataId" IS 'Rapid API GeoDB Cities';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."cities_id_seq"
OWNED BY "public"."cities"."id";
SELECT setval('"public"."cities_id_seq"', 157021, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."countries_id_seq"
OWNED BY "public"."countries"."id";
SELECT setval('"public"."countries_id_seq"', 250, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."regions_id_seq"
OWNED BY "public"."regions"."id";
SELECT setval('"public"."regions_id_seq"', 6, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."states_id_seq"
OWNED BY "public"."states"."id";
SELECT setval('"public"."states_id_seq"', 5457, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."subregions_id_seq"
OWNED BY "public"."subregions"."id";
SELECT setval('"public"."subregions_id_seq"', 22, true);

-- ----------------------------
-- Indexes structure for table cities
-- ----------------------------
CREATE INDEX "cities_country_id_idx" ON "public"."cities" USING btree (
  "country_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "cities_state_id_idx" ON "public"."cities" USING btree (
  "state_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table cities
-- ----------------------------
ALTER TABLE "public"."cities" ADD CONSTRAINT "cities_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table countries
-- ----------------------------
CREATE INDEX "countries_region_id_idx" ON "public"."countries" USING btree (
  "region_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "countries_subregion_id_idx" ON "public"."countries" USING btree (
  "subregion_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table countries
-- ----------------------------
ALTER TABLE "public"."countries" ADD CONSTRAINT "countries_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table regions
-- ----------------------------
ALTER TABLE "public"."regions" ADD CONSTRAINT "regions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table states
-- ----------------------------
CREATE INDEX "states_country_id_idx" ON "public"."states" USING btree (
  "country_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table states
-- ----------------------------
ALTER TABLE "public"."states" ADD CONSTRAINT "states_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table subregions
-- ----------------------------
CREATE INDEX "subregions_region_id_idx" ON "public"."subregions" USING btree (
  "region_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table subregions
-- ----------------------------
ALTER TABLE "public"."subregions" ADD CONSTRAINT "subregions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table cities
-- ----------------------------
ALTER TABLE "public"."cities" ADD CONSTRAINT "cities_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "public"."countries" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."cities" ADD CONSTRAINT "cities_state_id_fkey" FOREIGN KEY ("state_id") REFERENCES "public"."states" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table countries
-- ----------------------------
ALTER TABLE "public"."countries" ADD CONSTRAINT "countries_region_id_fkey" FOREIGN KEY ("region_id") REFERENCES "public"."regions" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."countries" ADD CONSTRAINT "countries_subregion_id_fkey" FOREIGN KEY ("subregion_id") REFERENCES "public"."subregions" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table states
-- ----------------------------
ALTER TABLE "public"."states" ADD CONSTRAINT "states_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "public"."countries" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table subregions
-- ----------------------------
ALTER TABLE "public"."subregions" ADD CONSTRAINT "subregions_region_id_fkey" FOREIGN KEY ("region_id") REFERENCES "public"."regions" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
