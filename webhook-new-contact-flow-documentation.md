# Flujo del Endpoint: `POST /wati/webhook/new-contact/:tenantId`

## 📋 Resumen
Este endpoint maneja webhooks entrantes de WATI cuando un nuevo contacto envía un mensaje. Su función principal es registrar el webhook, crear o actualizar usuarios, gestionar registros de campaña, y sincronizar datos de vuelta a WATI.

## 🔗 Endpoint
```
POST /wati/webhook/new-contact/:tenantId
```

### Parámetros
- `tenantId` (path): Identificador del tenant/campaña en WATI

### Payload Esperado
```typescript
{
  eventType: string;
  id: string;
  created: string;
  waId: string;        // Número de WhatsApp del contacto
  senderName: string;  // Nombre del contacto
  sourceId: string | null;
  sourceUrl: string | null;
  sourceType: number;
}
```

## 🎯 Flujo Detallado

### 1. **Recepción del Webhook** (`WatiController`)
- **Archivo**: `src/integrations/wati/wati.controller.ts:29-40`
- **Función**: `handleNewContactMessage()`
- **Acciones**:
  - Recibe el payload del webhook
  - Realiza logging del payload recibido
  - Convierte el payload a `NewContactMessageReceivedEvent`
  - Delega el procesamiento al `WatiService`

### 2. **Procesamiento Principal** (`WatiService`)
- **Archivo**: `src/integrations/wati/wati.service.ts:48-82`
- **Función**: `handleWebhookNewContactMessage()`

#### 2.1 Validación de Campaña
- Busca la campaña usando el `tenantId`
- Si no existe la campaña → **Error**: "Campaign not found"

#### 2.2 Registro del Webhook
- Crea una entidad `IncomingWebhook` con:
  - `provider`: "WATI"
  - `tenantId`: del parámetro
  - `eventType`: "newContactMessageReceived"
  - `rawPayload`: payload completo
  - `waId`: número de WhatsApp
- Guarda en la tabla `incoming_webhooks`

### 3. **Gestión de Usuario** (`UserService`)
- **Archivo**: `src/modules/user/user.service.ts:34-58`
- **Función**: `saveUser()`

#### 3.1 Búsqueda de Usuario
- Busca usuario existente por `phone` (usando `waId`)

#### 3.2 Usuario Existente
Si el usuario ya existe:
- Verifica si tiene registro en la campaña actual
- Si **tiene registro** → Retorna usuario existente
- Si **no tiene registro** → Crea nuevo registro de campaña

#### 3.3 Usuario Nuevo
Si el usuario no existe:
- Crea nuevo usuario con `phone = waId`
- Guarda en la tabla `users`
- Crea registro de campaña automáticamente

### 4. **Registro de Campaña** (`CampaignRegistrationsService`)
- **Archivo**: `src/modules/campaign-registrations/campaign-registrations.service.ts`
- **Función**: `insertCampaignRegistration()`

#### 4.1 Generación de Datos
- `invite_code`: UUID único generado
- `invite_link`: URL generada usando `generateInviteUrl()`
- `name`: del `senderName` del webhook
- `campaign_id` y `user_id`: relaciones correspondientes

#### 4.2 Persistencia
- Guarda en la tabla `campaign_registrations`
- Campos únicos: `invite_code` y `invite_link`

### 5. **Sincronización con WATI** 
- **Archivo**: `src/integrations/wati/wati.service.ts:84-130`
- **Función**: `updateContactAttributes()`

#### 5.1 Preparación
- Obtiene la campaña por `tenantId`
- Desencripta el token de la campaña usando `EncryptionService`
- Construye URL: `{WATI_BASE_URL}/{tenantId}/api/v1/updateContactAttributes/{waId}`

#### 5.2 Actualización de Atributos
Envía a WATI los siguientes atributos personalizados:
```json
{
  "customParams": [
    {
      "name": "invite_code",
      "value": "uuid-generado"
    },
    {
      "name": "invite_link", 
      "value": "url-de-invitacion"
    }
  ]
}
```

#### 5.3 Autenticación
- Headers: `Authorization: Bearer {token-desencriptado}`
- Content-Type: `application/json`

### 6. **Respuesta Final**
- Retorna `{ status: "ok" }` si todo es exitoso
- En caso de error, propaga la excepción

## 🗄️ Tablas Involucradas

### `incoming_webhooks`
```sql
- id (UUID, PK)
- provider (VARCHAR) = "WATI"
- tenantId (VARCHAR)
- eventType (VARCHAR) = "newContactMessageReceived"
- rawPayload (JSONB) -- Payload completo del webhook
- waId (VARCHAR) -- Número de WhatsApp
- created_at, updated_at, deleted_at
```

### `users`
```sql
- id (UUID, PK)
- phone (VARCHAR, UNIQUE) -- Número de WhatsApp
- created_at, updated_at, deleted_at
```

### `campaign_registrations`
```sql
- id (UUID, PK)
- campaign_id (UUID, FK)
- user_id (UUID, FK)
- name (VARCHAR) -- senderName del webhook
- invite_code (VARCHAR, UNIQUE) -- UUID generado
- invite_link (VARCHAR, UNIQUE) -- URL de invitación
- referred_by_user_id (UUID, nullable)
- registration_form (JSONB, nullable)
- created_at, updated_at, deleted_at
```

### `campaigns`
```sql
- id (UUID, PK)
- name (VARCHAR)
- wati_tenant_id (VARCHAR, UNIQUE) -- Usado para lookup
- token (TEXT) -- Token encriptado para WATI API
- candidate_id (UUID, FK)
- whatsapp_number (VARCHAR)
- is_active (BOOLEAN)
- start_date, end_date
- created_at, updated_at, deleted_at
```

## 🔄 Servicios y Dependencias

### Dependencias Principales
- **WatiService**: Lógica principal del webhook
- **UserService**: Gestión de usuarios
- **CampaignService**: Búsqueda de campañas
- **CampaignRegistrationsService**: Gestión de registros
- **EncryptionService**: Desencriptación de tokens

### Configuración Requerida
- `WATI_BASE_URL`: URL base de la API de WATI
- Token de campaña encriptado en la base de datos

## ⚠️ Casos de Error

### 1. Campaña No Encontrada
```
Error: "Campaign not found"
```
- **Causa**: No existe campaña con el `tenantId` proporcionado
- **Acción**: Verificar que el `tenantId` sea correcto

### 2. Token de Campaña Faltante
```
Error: "Campaign token not found"
```
- **Causa**: La campaña existe pero no tiene token configurado
- **Acción**: Configurar token en la campaña

### 3. Error en API de WATI
```
Error: "Failed to update contact attributes"
```
- **Causa**: Fallo en la comunicación con WATI o token inválido
- **Acción**: Verificar conectividad y validez del token

### 4. Error de Base de Datos
- **Causa**: Violación de constraints (invite_code/invite_link duplicados)
- **Acción**: Revisar lógica de generación de códigos únicos

## 🔧 Configuración y Variables

### Variables de Entorno
```env
WATI_BASE_URL=https://live-server-123456.wati.io
```

### Archivos de Configuración
- `src/config/graphql.config.ts`: Configuración de GraphQL
- `src/common/constants/routes.constants.ts`: Generación de URLs

## 📝 Logs y Debugging

### Logs Principales
1. "Received new contact message payload:" - Payload entrante
2. "Parsed payload:" - Payload procesado
3. "Saving user with waId:" - Proceso de guardado de usuario
4. "Creating campaign registration for user:" - Creación de registro
5. "Wati API response:" - Respuesta de WATI API

### Debugging Tips
- Verificar logs en consola para seguir el flujo
- Revisar tabla `incoming_webhooks` para webhooks recibidos
- Verificar tabla `users` para creación de usuarios
- Revisar tabla `campaign_registrations` para registros creados

## 🚀 Optimizaciones Posibles

1. **Caching**: Cachear campañas frecuentemente consultadas
2. **Batch Processing**: Procesar múltiples webhooks en lotes
3. **Retry Logic**: Implementar reintentos para fallos de WATI API
4. **Async Processing**: Procesar webhooks de forma asíncrona
5. **Validation**: Añadir validación más estricta del payload

---

**Última actualización**: 19 de Agosto, 2025
**Versión**: 1.0


