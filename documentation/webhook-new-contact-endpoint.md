# Documentación del Endpoint: POST /wati/webhook/new-contact/:tenantId

## Descripción General

Este endpoint maneja los webhooks de WATI cuando se recibe un mensaje de un nuevo contacto. Es parte del sistema de integración con la plataforma WATI para gestionar campañas políticas y registros de usuarios.

## Información del Endpoint

- **Método**: POST
- **Ruta**: `/wati/webhook/new-contact/:tenantId`
- **Controlador**: `WatiController`
- **Método**: `handleNewContactMessage`

## Parámetros

### Path Parameters
- `tenantId` (string): Identificador único del tenant/campaña que recibe el webhook

### Body
El payload contiene la información del evento de nuevo contacto enviado por WATI:

```typescript
interface NewContactMessageReceivedEvent {
  eventType: string;           // Tipo de evento: "newContactMessageReceived"
  id: string;                  // ID único del evento
  created: string;             // Fecha de creación del evento
  waId: string;               // WhatsApp ID del contacto
  senderName: string;         // Nombre del remitente
  sourceId: string | null;    // ID de la fuente (opcional)
  sourceUrl: string | null;   // URL de la fuente (opcional)
  sourceType: number;         // Tipo de fuente numérico
}
```

## Flujo de Procesamiento

### 1. Validación Inicial
- Se recibe el payload del webhook de WATI
- Se extrae el `tenantId` de los parámetros de la URL
- Se registra en consola el payload recibido para auditoría

### 2. Búsqueda de Campaña
- Se busca la campaña asociada al `tenantId`
- Si no se encuentra la campaña, se lanza una excepción

### 3. Registro del Webhook
- Se crea una nueva entrada en la tabla `incoming_webhooks`
- Se almacena:
  - Provider: "WATI"
  - Tenant ID
  - Tipo de evento: "newContactMessageReceived"
  - Payload completo en formato JSON
  - WhatsApp ID del contacto

### 4. Gestión del Usuario
- Se busca si existe un usuario con el número de teléfono (waId)
- **Si el usuario existe**:
  - Se verifica si ya tiene un registro en la campaña
  - Si no tiene registro, se crea uno nuevo
  - Se retorna el usuario existente
- **Si el usuario no existe**:
  - Se crea un nuevo usuario con el número de teléfono
  - Se crea automáticamente un registro de campaña para ese usuario

### 5. Actualización de Atributos en WATI
- Se obtiene el registro de campaña del usuario
- Se actualiza el contacto en WATI con:
  - `invite_code`: Código de invitación único generado
  - `invite_link`: Enlace de invitación asociado

## Respuesta

El endpoint retorna una respuesta simple confirmando el procesamiento:

```json
{
  "status": "ok"
}
```

## Diagrama de Flujo

```mermaid
flowchart TD
    A[WATI envía webhook] --> B[Recibir POST /wati/webhook/new-contact/:tenantId]
    B --> C[Extraer tenantId y payload]
    C --> D[Buscar campaña por tenantId]
    D --> E{¿Campaña existe?}
    E -->|No| F[Lanzar excepción]
    E -->|Sí| G[Crear registro en incoming_webhooks]
    G --> H[Buscar usuario por waId/teléfono]
    H --> I{¿Usuario existe?}
    I -->|Sí| J[Verificar registro en campaña]
    I -->|No| K[Crear nuevo usuario]
    J --> L{¿Tiene registro?}
    L -->|Sí| M[Retornar usuario existente]
    L -->|No| N[Crear registro de campaña]
    K --> O[Crear registro de campaña automáticamente]
    N --> P[Obtener registro de campaña]
    O --> P
    M --> P
    P --> Q[Actualizar atributos en WATI]
    Q --> R[Enviar invite_code e invite_link]
    R --> S[Retornar status: ok]
    F --> T[Error: Campaign not found]
```

## Entidades Involucradas

### 1. IncomingWebhook
- Almacena todos los webhooks recibidos para auditoría
- Campos principales: provider, tenantId, eventType, rawPayload, waId

### 2. Campaign
- Representa una campaña política
- Contiene token encriptado para API de WATI
- Asociada a candidatos y registros

### 3. User
- Representa usuarios del sistema
- Identificados principalmente por número de teléfono

### 4. CampaignRegistration
- Vincula usuarios con campañas
- Contiene códigos de invitación únicos y enlaces

## Consideraciones de Seguridad

1. **Token Encriptado**: El token de WATI se almacena encriptado en la base de datos
2. **Validación de Tenant**: Se valida que el tenant existe antes de procesar
3. **Auditoría**: Todos los webhooks se registran para trazabilidad

## Casos de Error

1. **Campaña no encontrada**: Cuando el tenantId no corresponde a ninguna campaña activa
2. **Token faltante**: Si la campaña no tiene token configurado para WATI
3. **Error en API de WATI**: Fallos al actualizar atributos del contacto
4. **Registro de campaña no encontrado**: Error interno si no se puede crear/encontrar el registro

## Dependencias

- **WatiService**: Lógica de negocio para integración con WATI
- **UserService**: Gestión de usuarios del sistema
- **CampaignService**: Operaciones con campañas
- **CampaignRegistrationsService**: Gestión de registros de campaña
- **EncryptionService**: Encriptación/desencriptación de tokens

## Logs y Debugging

El endpoint genera varios logs para facilitar el debugging:
- Payload recibido del webhook
- Información del usuario procesado
- Respuestas de la API de WATI
- Errores de procesamiento

## Uso Típico

Este endpoint es llamado automáticamente por WATI cuando:
1. Un usuario envía su primer mensaje a un número de WhatsApp de campaña
2. Se registra un nuevo contacto en el sistema WATI
3. Se activa un flujo de conversación automatizado

El resultado es que el usuario queda registrado en el sistema con su código de invitación único, listo para participar en la campaña política correspondiente.
