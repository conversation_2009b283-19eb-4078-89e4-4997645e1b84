# 🎯 **PROMPT DEL SISTEMA MINOS**

**MINOS** es una plataforma backend de referidos multinivel basada en WhatsApp, desarrollada en NestJS con PostgreSQL, diseñada inicialmente para campañas políticas pero extensible a otros sectores. El sistema integra chatbots de WhatsApp (vía WATI) con un backend robusto que gestiona redes de referidos jerárquicas, formularios dinámicos y análisis de conversaciones.

---

## 🚀 **CARACTERÍSTICAS PRINCIPALES**

### **🏗️ Arquitectura y Tecnologías**
- **Backend**: NestJS con TypeScript
- **Base de Datos**: PostgreSQL con TypeORM
- **API**: GraphQL (Apollo Server) + REST endpoints
- **Integración WhatsApp**: WATI API
- **Seguridad**: Encriptación AES-256-GCM para tokens sensibles
- **Acortador de URLs**: Integración con TinyURL API

### **📱 Funcionalidades Core**

#### **1. Sistema de Referidos Multinivel**
- Generación automática de códigos únicos de invitación (UUID)
- Enlaces personalizados para compartir (`/wati/invite/{codigo}`)
- Tracking de referidos jerárquicos con `referred_by_user_id`
- Red de referidos escalable y auditable

#### **2. Gestión de Campañas Políticas**
- **Múltiples campañas** simultáneas con números de WhatsApp independientes
- **Candidatos** asociados a partidos políticos
- **Fechas de campaña** configurables (inicio/fin)
- **Tenant isolation** por campaña usando `wati_tenant_id`

#### **3. Integración WhatsApp (WATI)**
- **Webhooks automáticos** para nuevos contactos
- **Formularios dinámicos** capturados vía WhatsApp
- **Sincronización bidireccional** de atributos de contacto
- **Mensajes personalizados** según perfil del usuario

#### **4. Gestión de Usuarios**
- **Identificación por teléfono** (número de WhatsApp)
- **Registro automático** al primer contacto
- **Historial de registros** en múltiples campañas
- **Datos de formulario** almacenados en formato JSONB

### **🔧 Módulos del Sistema**

#### **Módulos Principales**
1. **UserModule**: Gestión de usuarios y perfiles
2. **CampaignModule**: Administración de campañas
3. **CampaignRegistrationsModule**: Registros y códigos de invitación
4. **CandidateModule**: Gestión de candidatos políticos
5. **PoliticalPartyModule**: Partidos políticos
6. **WebhookModule**: Captura general de webhooks
7. **WatiModule**: Integración específica con WATI

#### **Servicios de Soporte**
- **EncryptionService**: Encriptación segura de tokens
- **TinyUrlService**: Acortamiento de enlaces
- **WatiService**: Comunicación con API de WATI

### **🗄️ Modelo de Datos**

#### **Entidades Principales**
- **Users**: Usuarios identificados por teléfono
- **Campaigns**: Campañas con configuración WATI
- **CampaignRegistrations**: Registros con códigos únicos
- **Candidates**: Candidatos políticos
- **PoliticalParties**: Partidos políticos
- **IncomingWebhooks**: Auditoría de webhooks recibidos

#### **Características de Datos**
- **UUIDs** como identificadores primarios
- **Auditoría automática** (created_at, updated_at, deleted_at)
- **Campos únicos** para códigos e invitaciones
- **JSONB** para formularios flexibles

### **🔄 Flujos de Trabajo**

#### **Flujo de Nuevo Contacto**
1. Usuario envía mensaje a WhatsApp de campaña
2. WATI envía webhook al backend
3. Sistema crea/actualiza usuario automáticamente
4. Genera código único de invitación
5. Sincroniza datos de vuelta a WATI
6. Usuario recibe su código para compartir

#### **Flujo de Formulario de Registro**
1. Usuario completa formulario en WhatsApp
2. Datos se almacenan en formato JSONB
3. Se actualiza registro de campaña
4. Información disponible para análisis

### **🛡️ Seguridad y Configuración**
- **Tokens encriptados** en base de datos
- **Variables de entorno** para configuración sensible
- **Validación de tenants** antes de procesar webhooks
- **Auditoría completa** de todas las interacciones

### **📊 Capacidades de Escalamiento**
- **Multi-tenant** por diseño
- **GraphQL** para consultas eficientes
- **Arquitectura modular** para extensibilidad
- **Preparado para SaaS** con configuración por campaña

### **🎯 Casos de Uso**
- **Campañas políticas** con redes de referidos
- **Marketing multinivel** vía WhatsApp
- **CRM conversacional** para análisis de población
- **Sistemas de invitación** escalables

### **🔗 Endpoints Principales**
- `POST /wati/webhook/new-contact/:tenantId` - Manejo de nuevos contactos
- `POST /wati/webhook/registration-form/:tenantId` - Procesamiento de formularios
- `GET /wati/invite/:inviteCode` - Redirección de invitaciones
- `GET /wati/open-chat/:whatsappNumber` - Apertura directa de chat

### **⚙️ Variables de Entorno Requeridas**
```env
# Base de datos
DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD, DB_DATABASE

# WATI Integration
WATI_BASE_URL, WATI_TOKEN, BACKEND_URL

# Seguridad
ENCRYPTION_KEY_BASE64

# TinyURL
TINYURL_API_BASE, TINYURL_API_TOKEN, TINYURL_TIMEOUT_MS
```

---

Este sistema está diseñado como un **MVP funcional** que puede escalarse como **SaaS personalizable** para diferentes sectores más allá de la política, con capacidad de manejar múltiples campañas simultáneas y redes de referidos complejas.
